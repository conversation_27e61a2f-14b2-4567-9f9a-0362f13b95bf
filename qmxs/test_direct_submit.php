<?php
// test_direct_submit.php - 直接测试主页面提交功能

require_once 'config.php';

echo "直接测试主页面提交功能\n";
echo "========================\n\n";

// 模拟POST数据
$_POST = [
    'recharge_account_name' => 'aishang',
    'recharge_panel_name' => '七猫小说充值模板',
    'postback_rule_name' => '七猫小说回传规则',
    'project' => '8',
    'app_name' => '七猫小说',
    'media_id' => '1',
    'book_names' => "直接提交测试A\n直接提交测试B",
    'advertiser_account_ids' => "*********\n555666778",
    'book_ids' => "515208\n515208",
    'chapter_numbers' => "1\n2"
];

// 模拟服务器环境变量
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/qmxs/test_direct_submit.php';

echo "模拟POST数据:\n";
foreach ($_POST as $key => $value) {
    echo "$key: " . str_replace("\n", "\\n", $value) . "\n";
}
echo "\n";

echo "开始执行主页面逻辑...\n";

// 捕获输出
ob_start();

try {
    // 包含主页面的处理逻辑
    include_once 'index.php';
} catch (Exception $e) {
    echo "执行过程中发生异常: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();

echo "执行完成\n";
echo "输出长度: " . strlen($output) . " 字节\n";

// 检查输出类型
if (strpos($output, '<?xml') === 0) {
    echo "✅ 输出是Excel XML格式\n";
    
    // 保存Excel文件
    $filename = 'direct_submit_result_' . date('Y-m-d_H-i-s') . '.xls';
    file_put_contents($filename, $output);
    echo "✅ Excel文件已保存: $filename\n";
    
} else if (strpos($output, '<script>alert(') !== false) {
    echo "❌ 输出包含错误提示\n";
    
    // 提取alert中的错误信息
    if (preg_match("/alert\('([^']+)'\)/", $output, $matches)) {
        echo "错误信息: " . $matches[1] . "\n";
    }
    
} else if (strpos($output, '<!DOCTYPE html') === 0) {
    echo "⚠️ 输出是HTML页面\n";
    
} else {
    echo "⚠️ 输出格式未知\n";
}

// 显示输出的前500字符
echo "\n输出内容预览:\n";
echo "----------------------------------------\n";
echo substr($output, 0, 500);
if (strlen($output) > 500) {
    echo "\n... (内容被截断)";
}
echo "\n----------------------------------------\n";

// 检查数据库状态
echo "\n数据库状态检查:\n";
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查create_promotion_link表
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link WHERE name LIKE '直接提交%'");
    $stmt->execute();
    $createCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "create_promotion_link表中的测试记录: $createCount 条\n";
    
    // 检查link表
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link WHERE name LIKE '直接提交%'");
    $stmt->execute();
    $linkCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "link表中的测试记录: $linkCount 条\n";
    
    if ($createCount > 0 && $linkCount == 0) {
        echo "⚠️ 数据已插入create_promotion_link表，但没有生成推广链接到link表\n";
    } else if ($linkCount > 0) {
        echo "✅ 推广链接已成功生成到link表\n";
        
        // 显示生成的链接
        $stmt = $pdo->prepare("SELECT name, link, advertiser_account_id FROM link WHERE name LIKE '直接提交%' ORDER BY id DESC LIMIT 5");
        $stmt->execute();
        $links = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\n生成的推广链接:\n";
        foreach ($links as $link) {
            echo "- " . $link['name'] . " (广告主ID: " . $link['advertiser_account_id'] . ")\n";
            echo "  链接: " . $link['link'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 数据库检查失败: " . $e->getMessage() . "\n";
}

echo "\n测试完成\n";
?>
