<?php
// 在文件顶部新增强制UTF-8编码声明
header('Content-Type: application/json; charset=utf-8');
// get_recharge_templates.php - 获取充值模板列表

// 引入配置文件
require_once '../config.php';

function generateSign($params, $secretKey) {
    try {
        $filteredParams = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });

        ksort($filteredParams);

        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= "{$key}={$value}&";
        }

        $signStr = rtrim($signStr, '&');

        $signStrWithSecret = $signStr . $secretKey;
        $sign = strtolower(md5($signStrWithSecret));

        return $sign;
    } catch (Exception $e) {
        // 修改为直接抛出异常
        throw $e;
    }
}

$params = [
    'admin_account_name' => isset($_GET['admin_account_name']) ? $_GET['admin_account_name'] : ADMIN_ACCOUNT_NAME,
    'project' => isset($_GET['project']) ? (int)$_GET['project'] : PROJECT_ID,
    'appid' => isset($_GET['appid']) ? $_GET['appid'] : WX_APPID,
    'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
    'page_size' => isset($_GET['page_size']) ? (int)$_GET['page_size'] : 100,
    'access_key' => ACCESS_KEY,
    'timestamp' => time(),  // 动态生成时间戳
    'random' => mt_rand(*********, *********),  // 动态生成随机数
];

// 新增：检查必要参数是否有效
if (empty($params['admin_account_name']) || empty($params['project']) || empty($params['appid'])) {
    ob_clean();
    echo json_encode([
        'code' => -1,
        'msg' => '缺少必要参数',
        'request_id' => uniqid('req-', true)
    ]);
    exit;
}

// 将sign参数移动到params数组最后生成（确保签名时不包含sign字段）
$params['sign'] = generateSign($params, SECRET_KEY);

// 构造完整的API请求URL
$api_url = API_BASE_URL . '/mapi/v1/recharge-panel/list'; 
$query_string = http_build_query($params);
$full_api_url = $api_url . '?' . $query_string;

// 发送GET请求
$ch = curl_init($full_api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

// 新增：检查cURL执行状态
if ($response === false) {
    ob_clean();
    echo json_encode([
        'code' => -1,
        'msg' => 'CURL请求失败: 响应为空',
        'request_id' => uniqid('req-', true)
    ]);
    exit;
}

// 新增：验证JSON格式
if (json_decode($response) === null) {
    ob_clean();
    echo json_encode([
        'code' => -1,
        'msg' => 'API返回无效JSON',
        'raw_response' => mb_substr($response, 0, 200),
        'request_id' => uniqid('req-', true)
    ]);
    exit;
}

// 解析JSON响应
$result = json_decode($response, true);

// 修改响应结构适配新接口规范
if ($result['code'] === 0) {
    // 获取数据库连接
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        die("连接失败: " . $conn->connect_error);
    }

    // 新增充值模板写入逻辑开始
    try {
        // 设置字符集（新增）
        $conn->set_charset("utf8mb4");
        
        // 准备插入语句（移除IGNORE关键字以便捕获错误）
        $insert_sql = "INSERT INTO recharge_templates 
                      (account_id, account_name, project, appid, app_name, template_id, template_name)
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
        // 修改：将stmt定义移到循环外并增加错误检查
        $stmt = $conn->prepare($insert_sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        foreach ($result['data']['list'] as $template) {
            // 过滤含有JS:前缀的字段
            $skip = false;
            foreach ($template as $key => $value) {
                if (is_string($value) && strpos($value, 'JS:') === 0) {
                    $skip = true;
                    break;
                }
            }
            if ($skip) continue;

            // 参数类型转换
            $accountId = intval($template['account_id']);
            // 新增显式字符串转换
            $templateName = (string)$template['name'];
            
            // 修改类型标识符为正确格式（最后一个参数改为s）
            $stmt->bind_param(
                "issssss",  // 修改：将最后一个i改为s以匹配字符串类型
                $accountId, 
                $template['account_name'], 
                $template['project'], 
                $template['appid'], 
                $template['app_name'], 
                intval($template['id']), 
                $templateName  // 使用显式转换后的变量
            );

            // 修改：在bind_param后增加错误检查
            if (!$stmt->execute()) {
                // 增加完整参数日志输出
                error_log("模板插入错误: " . $stmt->error . " 完整参数: " . json_encode([
                    'account_id' => $accountId,
                    'account_name' => $template['account_name'],
                    'project' => $template['project'],
                    'appid' => $template['appid'],
                    'app_name' => $template['app_name'],
                    'template_id' => intval($template['id']),
                    'template_name' => $templateName
                ]));
                continue;
            }
        }
    } catch (Exception $e) {
        error_log("充值模板写入异常: " . $e->getMessage());
    }
    // 新增充值模板写入逻辑结束

    $templates = $result['data']['list'] ?? [];
    $pagination = $result['data']['pagination'] ?? null; 
    
    $responseData = [
        'list' => [],
        'pagination' => $pagination ? [ 
            'page' => (int)$pagination['page'],
            'page_size' => (int)$pagination['page_size'],
            'total_count' => (int)$pagination['total_count']
        ] : null
    ];

    if (!empty($templates)) {
        $uniqueNames = [];
        
        foreach ($templates as $template) {
            // 强制类型转换确保符合接口规范
            $accountId = (string)$template['account_id'];
            $accountName = (string)$template['account_name'];
            $project = (int)$template['project'];  // 强制转为整数
            $appid = (string)$template['appid'];
            $app_name = (string)$template['app_name'];
            $templateId = (string)$template['id'];
            $templateName = (string)$template['name'];
            
            // 验证必要字段完整性
            if (empty($accountId) || empty($accountName) || !isset($project)) {
                continue;
            }
            
            if (!in_array($accountName, $uniqueNames)) {
                $responseData['list'][] = [
                    'account_id' => $accountId,
                    'account_name' => $accountName,
                    'project' => $project,
                    'appid' => $appid,
                    'app_name' => $app_name,
                    'id' => $templateId,
                    'name' => $templateName
                ];
                $uniqueNames[] = $accountName;
            }
        }
    }

    // 构建标准化响应
    $finalResponse = [
        'code' => 0,
        'msg' => $result['msg'] ?? 'success',
        'data' => $responseData,
        'request_id' => uniqid('req-', true)
    ];

    // 使用严格JSON编码参数
    $output = json_encode($finalResponse, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    if ($output === false) {
        ob_clean();
        error_log("JSON编码失败: " . json_last_error_msg());
        echo json_encode([
            'code' => -1,
            'msg' => '响应数据编码失败',
            'request_id' => uniqid('req-', true)
        ]);
        exit;
    }
    echo $output;
} else {
    // 错误响应保持结构一致性
    ob_clean();
    $errorResponse = [
        'code' => $result['code'] ?? -1,
        'msg' => $result['msg'] ?? 'API调用失败',
        'data' => null,
        'request_id' => uniqid('req-', true)
    ];
    $errorOutput = json_encode($errorResponse, JSON_UNESCAPED_UNICODE);
    if ($errorOutput === false) {
        ob_clean();
        echo '{"code":-1,"msg":"致命错误: 响应数据编码失败"}';
        exit;
    }
    echo $errorOutput;
}

// 修改：优化API响应验证逻辑
$result = json_decode($response, true);
if ($result === null && json_last_error() !== JSON_ERROR_NONE) {
    ob_clean();
    echo json_encode([
        'code' => -1,
        'msg' => 'API返回无效JSON: ' . json_last_error_msg(),
        'raw_response' => mb_substr($response, 0, 200),
        'request_id' => uniqid('req-', true)
    ]);
    exit;
}

// 修改：修复非数字值警告（221行附近）
$pagination = $result['data']['pagination'] ?? null; 
$responseData = [
    'list' => [],
    'pagination' => $pagination ? [ 
        'page' => isset($pagination['page']) ? (int)$pagination['page'] : 1,
        'page_size' => isset($pagination['page_size']) ? (int)$pagination['page_size'] : 100,
        'total_count' => isset($pagination['total_count']) ? (int)$pagination['total_count'] : 0
    ] : null
];
