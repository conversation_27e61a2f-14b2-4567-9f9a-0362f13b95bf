<?php
// test_insert.php - 临时测试页：验证中英文字符存储

// 设置内部编码
mb_internal_encoding('UTF-8');

// 直接连接数据库
require_once '../config.php';

// 显式声明页面编码
header('Content-Type: text/html; charset=UTF-8');

// 处理测试表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $testName = $_POST['test_name'] ?? '';
    
    // 强制编码转换（调试模式）
    $convertedName = mb_convert_encoding($testName, 'UTF-8', 'UTF-8,ASCII,auto');
    
    // 调试输出
    echo "<h3>调试信息：</h3>";
    echo "原始输入：" . htmlspecialchars($testName) . "<br>";
    echo "编码转换后：" . htmlspecialchars($convertedName) . "<br>";
    echo "mb_detect_encoding：" . mb_detect_encoding($testName, ['UTF-8', 'ASCII', 'auto']) . "<br>";
    
    // 数据库测试
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
        if ($conn->connect_error) {
            throw new Exception("连接失败: " . $conn->connect_error);
        }
        
        // 强制设置字符集
        $conn->set_charset("utf8mb4");
        $conn->query("SET NAMES 'utf8mb4'");
        
        // 新增：定义并赋值 admin_account_name
        $adminName = "test_admin"; // 示例值，可根据需求调整
    
        // 测试插入 - 修改SQL语句包含所有字段并设置默认值
        $stmt = $conn->prepare("INSERT INTO create_promotion_link (
            admin_account_name, account_id, project, appid, 
            book_id, chapter_num, name, media_id, 
            postback_rule_id, recharge_panel_id, advertiser_account_id
        ) VALUES (?, ?, 0, '0', 0, 0, ?, 0, 0, 0, 0)");
        
        // 修改bind_param的类型字符串为"sis"，匹配三个参数：字符串、整数、字符串
        $accountId = 1; // 示例值，可根据需求调整
        $stmt->bind_param("sis", $adminName, $accountId, $convertedName);
        
        if ($stmt->execute()) {
            echo "<p style='color:green;'>✅ 数据库插入成功！</p>";
        } else {
            throw new Exception("插入失败: " . $stmt->error);
        }
    } catch (Exception $e) {
        echo "<p style='color:red;'>❌ 错误: " . $e->getMessage() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>字符存储测试</title>
</head>
<body>
    <h1>中英文字符存储测试</h1>
    <form method="post">
        <div style="margin-bottom: 20px;">
            <label for="test_name">输入测试字符（支持中英文）：</label><br>
            <input type="text" id="test_name" name="test_name" value="<?= htmlspecialchars($_POST['test_name'] ?? '') ?>" style="width: 100%; padding: 8px;">
        </div>
        <button type="submit" style="padding: 10px 20px;">提交测试</button>
    </form>
    
    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
    <div style="margin-top: 30px;">
        <h3>存储结果预览：</h3>
        <pre><?= htmlspecialchars($convertedName) ?></pre>
    </div>
    <?php endif; ?>
</body>
</html>