<?php
// 在文件顶部新增强制UTF-8编码声明
header('Content-Type: application/json; charset=utf-8');
header("Cache-Control: no-cache, must-revalidate"); // 新增强制缓存失效
header("Expires: -1"); // 新增过期时间设置

// 新增唯一请求ID生成
$requestId = uniqid('req-', true);

// get_promotion_links.php - 获取推广链接列表
require_once '../config.php';

// 在API响应处理前初始化数据库连接
try {
    // 使用config.php中的配置创建PDO连接
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    error_log("Database Connection Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'msg' => '数据库连接失败: ' . $e->getMessage(),
        'request_id' => $requestId
    ]);
    exit;
}

// 修改签名生成函数，适配七猫小说签名规则
function generateSign($params, $secretKey) {
    try {
        // 过滤空值参数并按参数名排序
        $filteredParams = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });

        // 按参数名升序排序
        ksort($filteredParams);

        // 生成待签名字符串
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= "{$key}={$value}&";
        }
        // 移除末尾的&
        $signStr = rtrim($signStr, '&');

        // 拼接secret_key并生成MD5签名
        $signStrWithSecret = $signStr . $secretKey;
        $sign = strtolower(md5($signStrWithSecret));

        // 新增调试日志输出完整签名字符串
        error_log("Sign String: {$signStrWithSecret}");
        error_log("Sign Value: {$sign}");

        return $sign;
    } catch (Exception $e) {
        error_log("Sign Generation Error: " . $e->getMessage());
        // 降级处理：返回基础签名
        return strtolower(md5($params['access_key'] . $secretKey . $params['timestamp']));
    }
}

// 新增account_id和account_name参数
$params = [
    'access_key' => ACCESS_KEY,
    'admin_account_name' => isset($_GET['admin_account_name']) ? $_GET['admin_account_name'] : ADMIN_ACCOUNT_NAME, 
    'account_id' => isset($_GET['account_id']) ? $_GET['account_id'] : null,
    'account_name' => isset($_GET['account_name']) ? $_GET['account_name'] : null,
    'create_time_start' => isset($_GET['create_time_start']) ? $_GET['create_time_start'] : null,
    'create_time_end' => isset($_GET['create_time_end']) ? $_GET['create_time_end'] : null,
    'last_modify_time_start' => isset($_GET['last_modify_time_start']) ? $_GET['last_modify_time_start'] : null,
    'last_modify_time_end' => isset($_GET['last_modify_time_end']) ? $_GET['last_modify_time_end'] : null,
    'page' => isset($_GET['page']) ? $_GET['page'] : 1,
    'page_size' => isset($_GET['page_size']) ? $_GET['page_size'] : 20,
    'timestamp' => time(),  // 动态生成时间戳
    'random' => mt_rand(*********, *********)  // 动态生成随机数
];

// 保留微信参数组继承逻辑
foreach (['weapp'] as $group) {
    foreach (['admin_account_name', 'account_id', 'account_name', 'create_time_start', 
             'create_time_end', 'last_modify_time_start', 'last_modify_time_end', 
             'page', 'page_size'] as $paramKey) {
        if (isset($params[$paramKey])) {
            $paramsGroup[$group][$paramKey] = $params[$paramKey];
        }
    }
}

// 新增抖音参数组
$paramsDy = [
    'project' => PROJECT_ID,
    'access_key' => ACCESS_KEY,
    'admin_account_name' => isset($_GET['admin_account_name']) ? $_GET['admin_account_name'] : ADMIN_ACCOUNT_NAME, 
    'account_id' => isset($_GET['account_id']) ? $_GET['account_id'] : null,
    'account_name' => isset($_GET['account_name']) ? $_GET['account_name'] : null,
    'create_time_start' => isset($_GET['create_time_start']) ? $_GET['create_time_start'] : null,
    'create_time_end' => isset($_GET['create_time_end']) ? $_GET['create_time_end'] : null,
    'last_modify_time_start' => isset($_GET['last_modify_time_start']) ? $_GET['last_modify_time_start'] : null,
    'last_modify_time_end' => isset($_GET['last_modify_time_end']) ? $_GET['last_modify_time_end'] : null,
    'page' => isset($_GET['page']) ? $_GET['page'] : 1,
    'page_size' => isset($_GET['page_size']) ? $_GET['page_size'] : 100,
    'timestamp' => time(),  // 动态生成时间戳
    'random' => mt_rand(*********, *********)  // 动态生成随机数
];

// 新增抖音缓存键
$douyinCacheKey = 'promotion_links_dy_' . md5(serialize($paramsDy));

// 删除douyin组签名生成逻辑
foreach ($paramsGroup as $group => $groupParams) {
    $paramsGroup[$group]['timestamp'] = time();
    $paramsGroup[$group]['random'] = mt_rand(*********, *********);
    $paramsGroup[$group]['sign'] = generateSign($groupParams, SECRET_KEY);
}

// 删除douyin参数组生成
$paramsWeapp = $params;
$paramsWeapp['project'] = (int)PROJECT_ID;
$paramsWeapp['sign'] = generateSign($paramsWeapp, SECRET_KEY);

// 添加时间范围默认值逻辑
if (
    !isset($_GET['create_time_start']) &&
    !isset($_GET['create_time_end']) &&
    !isset($_GET['last_modify_time_start']) &&
    !isset($_GET['last_modify_time_end'])
) {
    // 查询近三天的时间范围
    $threeDaysAgo = strtotime('-3 days'); // 当前时间减去3天
    $currentTime = time(); // 当前时间
    $params['create_time_start'] = $threeDaysAgo;
    $params['create_time_end'] = $currentTime;
}

// 验证分页参数范围
if (isset($_GET['page'])) {
    $params['page'] = max(1, min((int)$_GET['page'], PHP_INT_MAX));
}
if (isset($_GET['page_size'])) {
    $params['page_size'] = max(1, min((int)$_GET['page_size'], 100));
}

// API请求处理
$api_url = API_BASE_URL . '/mapi/v2/promotion-link/list';


// 新增签名调试日志
error_log("Generated Sign: {$paramsWeapp['sign']} with params: " . json_encode($paramsWeapp));

// 构建带签名的API请求
$query_string = http_build_query($paramsWeapp);  
$full_api_url = $api_url . '?' . $query_string;

$ch = curl_init($full_api_url);

// 保持cURL配置一致性
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HEADER => false,  // 禁止返回HTTP头
    CURLOPT_FOLLOWLOCATION => true  // 跟随重定向
]);

// 修改API响应处理部分
$response = curl_exec($ch);
if ($response === false) {
    $error_msg = curl_error($ch);
    $errorOutput = json_encode([
        'code' => -1,
        'msg' => 'CURL请求失败: ' . $error_msg,
        'raw_response' => $response,
        'request_id' => $requestId
    ]);
    http_response_code(500);
    echo $errorOutput;
    curl_close($ch);
    exit;
}
curl_close($ch);

// 新增响应格式校验
if (empty($response) || !is_string($response)) {
    error_log("Invalid API Response: " . substr($response, 0, 100));
    echo json_encode([
        'code' => -1,
        'msg' => 'API返回数据异常，请检查请求参数',
        'request_id' => $requestId,
        'raw_response' => base64_encode($response) // 记录原始响应base64编码
    ]);
    exit;
}

// 检查JSON解析是否成功
$result = json_decode($response, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("JSON Parse Error: " . json_last_error_msg());
    echo json_encode([
        'code' => -1,
        'msg' => 'JSON解析失败: ' . json_last_error_msg(),
        'request_id' => $requestId,
        'raw_response' => base64_encode($response) // 记录原始响应base64编码
    ]);
    exit;
}

header('Content-Type: application/json');

if ($result['code'] === 0) {
    $links = $result['data']['list'] ?? [];
    
    $responseData = [
        'list' => []
    ];

    if (empty($links)) {
    } else {
        foreach ($links as $link) {
            // 新增过滤逻辑：跳过含有"JS:"前缀的account_id
            if (strpos($link['account_id'], 'JS:') === 0) {
                continue;
            }

            // 过滤含有"JS:"前缀的字段
            $skip = false;
            foreach ($link as $key => $value) {
                if (is_string($value) && strpos($value, 'JS:') === 0) {
                    $skip = true;
                    break;
                }
            }
            if ($skip) continue;

            $accountId = (string)$link['account_id'];
            $accountName = (string)$link['account_name'];
            $project = (int)$link['project'];
            $appId = (string)$link['appid'];
            $appName = (string)$link['app_name'];
            $id = (string)$link['id'];
            $name = (string)$link['name'];
            $createTime = date('Y-m-d H:i:s', (int)$link['create_time']); // 将时间戳转为日期格式
            $lastModifiedTime = date('Y-m-d H:i:s', (int)$link['last_modified_time']); // 将时间戳转为日期格式
            $bookId = (int)$link['book_id'];
            $bookName = (string)$link['book_name'];
            $chapterNum = (int)$link['chapter_num'];
            $mediaId = (int)$link['media_id'];
            $postbackRuleName = (string)$link['postback_rule_name'];
            $rechargePanelName = (string)$link['recharge_panel_name'];
            $linkUrl = (string)$link['link'];
            $launchPage = isset($link['launch_page']) && is_string($link['launch_page']) ? $link['launch_page'] : '';
            $launchParam = isset($link['launch_param']) && is_string($link['launch_param']) ? $link['launch_param'] : '';
            $adMonitorClickLink = isset($link['ad_monitor_click_link']) && is_string($link['ad_monitor_click_link']) ? $link['ad_monitor_click_link'] : '';

            // 新增字段过滤逻辑
            if (strpos($name, 'JS:') !== false) {
                continue;  // 跳过包含JS:的记录
            }

            // 使用预处理语句防止SQL注入
            try {
                $stmt = $pdo->prepare("INSERT IGNORE INTO promotion_links 
                    (account_id, account_name, project, appid, app_name, promotion_link_id, 
                    name, create_time, last_modified_time, book_id, book_name, chapter_num, 
                    media_id, postback_rule_name, recharge_panel_name, link, launch_page, 
                    launch_param, ad_monitor_click_link)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $accountId, $accountName, $project, $appId, $appName, $id,
                    $name, $createTime, $lastModifiedTime, $bookId, $bookName, $chapterNum,
                    $mediaId, $postbackRuleName, $rechargePanelName, $linkUrl, $launchPage,
                    $launchParam, $adMonitorClickLink
                ]);
            } catch (PDOException $e) {
                error_log("DB Insert Error: " . $e->getMessage());
                // 记录详细错误日志
                error_log("Failed Query: " . $stmt->queryString);
                error_log("Params: " . json_encode([
                    $accountId, $accountName, $project, $appId, $appName, $id,
                    $name, $createTime, $lastModifiedTime, $bookId, $bookName, $chapterNum,
                    $mediaId, $postbackRuleName, $rechargePanelName, $linkUrl, $launchPage,
                    $launchParam, $adMonitorClickLink
                ]));
            }

            // 构建完整的PromotionLink对象
            $promotionLink = [
                'account_id' => $accountId,
                'account_name' => $accountName,
                'project' => $project,
                'appid' => $appId,
                'app_name' => $appName,
                'id' => $id,
                'name' => $name,
                'create_time' => $createTime, 
                'last_modified_time' => $lastModifiedTime,
                'book_id' => $bookId,
                'book_name' => $bookName,
                'chapter_num' => $chapterNum,
                'media_id' => $mediaId,
                'postback_rule_name' => $postbackRuleName,
                'recharge_panel_name' => $rechargePanelName,
                'link' => $linkUrl,
                'launch_page' => $launchPage,
                'launch_param' => $launchParam,
                'ad_monitor_click_link' => $adMonitorClickLink
            ];

            $responseData['list'][] = $promotionLink;
        }
    }

    // 添加分页信息
    $pagination = [
        'page' => $params['page'],
        'page_size' => $params['page_size'],
        'total_count' => count($links)
    ];

    // 统一响应结构
    $finalResponse = [
        'code' => 0,
        'msg' => $result['msg'] ?? 'success',
        'data' => [
            'list' => $responseData['list'],
            'pagination' => $pagination
        ],
        'request_id' => $requestId
    ];
    
    $output = json_encode($finalResponse, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    ob_clean(); // 清理输出缓冲区
    echo $output;
    exit;

} else {
    // 错误响应保持原有结构
    $errorResponse = [
        'code' => $result['code'] ?? -1,
        'msg' => $result['msg'] ?? 'API调用失败',
        'data' => null,
        'request_id' => $requestId
    ];
    
    $errorOutput = json_encode($finalResponse, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    ob_clean(); // 清理输出缓冲区
    echo $errorOutput;
    exit;

}
