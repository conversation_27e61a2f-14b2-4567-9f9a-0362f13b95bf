<?php
// 在文件顶部新增强制UTF-8编码声明
header('Content-Type: application/json; charset=utf-8');
// get_recharge_templates.php - 获取充值模板列表

// 引入配置文件
require_once '../config.php';

function generateSign($params, $secretKey) {
    try {
        $filteredParams = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });

        ksort($filteredParams);

        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= "{$key}={$value}&";
        }

        $signStr = rtrim($signStr, '&');

        $signStrWithSecret = $signStr . $secretKey;
        $sign = strtolower(md5($signStrWithSecret));

        return $sign;
    } catch (Exception $e) {
        throw $e;
    }
}

$params = [
    'admin_account_name' => isset($_GET['admin_account_name']) ? $_GET['admin_account_name'] : ADMIN_ACCOUNT_NAME,
    'project' => isset($_GET['project']) ? (int)$_GET['project'] : PROJECT_ID,
    'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
    'page_size' => isset($_GET['page_size']) ? (int)$_GET['page_size'] : 100,
    'access_key' => ACCESS_KEY,
    'timestamp' => time(),
    'random' => mt_rand(*********, *********),
];

// 添加调试信息输出请求参数
error_log("Request Params: " . json_encode($params));

// 检查必要参数是否有效
if (empty($params['admin_account_name']) || empty($params['project'])) {
    ob_clean();
    echo json_encode([
        'code' => -1,
        'msg' => '缺少必要参数',
        'request_id' => uniqid('req-', true)
    ]);
    exit;
}

// 强制返回全量数据
if(isset($_GET['fetchAll']) || (isset($_GET['page']) && $_GET['page'] == 1)) {
    $params['page_size'] = 1000;
}

// 生成签名
$params['sign'] = generateSign($params, SECRET_KEY);

// 构造完整的API请求URL
$api_url = API_BASE_URL . '/mapi/v1/recharge-panel/list';
$query_string = http_build_query($params);
$full_api_url = $api_url . '?' . $query_string;

// 发送GET请求
$ch = curl_init($full_api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

// 检查cURL执行状态
if ($response === false) {
    ob_clean();
    echo json_encode([
        'code' => -1,
        'msg' => 'CURL请求失败: 响应为空',
        'request_id' => uniqid('req-', true)
    ]);
    exit;
}

// 解析JSON响应
$result = json_decode($response, true);

// 验证JSON格式
if ($result === null && json_last_error() !== JSON_ERROR_NONE) {
    ob_clean();
    echo json_encode([
        'code' => -1,
        'msg' => 'API返回无效JSON: ' . json_last_error_msg(),
        'raw_response' => mb_substr($response, 0, 200),
        'request_id' => uniqid('req-', true)
    ]);
    exit;
}

// 调试信息输出API响应数据
error_log("API Response: " . json_encode($result));

// 处理API响应
if ($result['code'] === 0) {
    // 获取数据库连接
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        ob_clean();
        echo json_encode([
            'code' => -1,
            'msg' => '数据库连接失败: ' . $conn->connect_error,
            'request_id' => uniqid('req-', true)
        ]);
        exit;
    }

    // 设置字符集
    $conn->set_charset("utf8mb4");

    // 如果API返回了数据，先存储到数据库
    if (!empty($result['data']['list'])) {
        try {
            // 准备插入语句 - 修正唯一键约束，确保同一子账号的多条不同数据都能存入
            $insert_sql = "INSERT INTO recharge_templates
                          (account_id, account_name, project, appid, app_name, template_id, template_name)
                          VALUES (?, ?, ?, ?, ?, ?, ?)
                          ON DUPLICATE KEY UPDATE
                          account_name = VALUES(account_name),
                          app_name = VALUES(app_name),
                          template_name = VALUES(template_name)";
            $stmt = $conn->prepare($insert_sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            $insertedCount = 0;
            $skippedCount = 0;
            $totalCount = count($result['data']['list']);

            foreach ($result['data']['list'] as $template) {
                // 过滤含有"JS:"前缀的字段
                $skip = false;
                foreach ($template as $key => $value) {
                    if (is_string($value) && strpos($value, 'JS:') === 0) {
                        $skip = true;
                        break;
                    }
                }
                if ($skip) {
                    $skippedCount++;
                    continue;
                }

                // 参数类型转换
                $accountId = intval($template['account_id']);
                $accountName = (string)$template['account_name'];
                $project = (string)$template['project'];
                $appid = (string)$template['appid'];
                $appName = (string)$template['app_name'];
                $templateId = intval($template['id']);
                $templateName = (string)$template['name'];

                // 绑定参数并执行
                $stmt->bind_param(
                    "issssis",
                    $accountId,
                    $accountName,
                    $project,
                    $appid,
                    $appName,
                    $templateId,
                    $templateName
                );

                if (!$stmt->execute()) {
                    error_log("充值模板插入错误: " . $stmt->error . " 参数: " . json_encode([
                        'account_id' => $accountId,
                        'account_name' => $accountName,
                        'project' => $project,
                        'appid' => $appid,
                        'app_name' => $appName,
                        'template_id' => $templateId,
                        'template_name' => $templateName
                    ]));
                } else {
                    $insertedCount++;
                }
            }

            // 记录数据处理统计信息
            error_log("充值模板数据处理完成 - 总数: {$totalCount}, 插入/更新: {$insertedCount}, 跳过: {$skippedCount}");
        } catch (Exception $e) {
            error_log("充值模板写入异常: " . $e->getMessage());
        }
    }

    // 从数据库查询去重后的 app_name
    $query = "SELECT DISTINCT app_name FROM recharge_templates WHERE app_name IS NOT NULL AND app_name != ''";
    $result_set = $conn->query($query);

    $appNames = [];
    if ($result_set) {
        while ($row = $result_set->fetch_assoc()) {
            $appNames[] = $row['app_name'];
        }
    }

    // 构建响应数据
    $finalResponse = [
        'code' => 0,
        'msg' => 'success',
        'data' => [
            'list' => array_map(function($name) { return ['app_name' => $name]; }, $appNames),
            'pagination' => $result['data']['pagination'] ?? []
        ],
        'request_id' => uniqid('req-', true)
    ];

    $conn->close();
    echo json_encode($finalResponse, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    exit;
} else {
    // 错误响应
    ob_clean();
    $errorResponse = [
        'code' => $result['code'] ?? -1,
        'msg' => $result['msg'] ?? 'API调用失败',
        'data' => null,
        'request_id' => uniqid('req-', true)
    ];
    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    exit;
}
