<?php
// 在文件顶部新增强制UTF-8编码声明
header('Content-Type: application/json; charset=utf-8');
// create_promotion_link.php - 批量创建推广链接
// 引入配置文件
require_once '../config.php';

// 检查数据库配置是否定义
if (!defined('DB_HOST') || !defined('DB_USER') || !defined('DB_PASSWORD') || !defined('DB_NAME')) {
    die(json_encode([
        'code' => -4,
        'msg' => '数据库配置未定义，请检查 config.php 文件',
        'request_id' => uniqid('req_', true)
    ]));
}

// 检查 ACCESS_KEY 是否定义
if (!defined('ACCESS_KEY')) {
    die(json_encode([
        'code' => -4,
        'msg' => 'ACCESS_KEY 未定义，请检查 config.php 文件',
        'request_id' => uniqid('req_', true)
    ]));
}

// 初始化PDO连接
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    error_log("Database Connection Error: " . $e->getMessage());
    die(json_encode([
        'code' => -1,
        'msg' => '数据库连接失败: ' . $e->getMessage(),
        'request_id' => uniqid('req_', true)
    ]));
}

// 批量查询表中所有待处理的参数
try {
    $stmt = $pdo->prepare("SELECT
        id,
        admin_account_name,
        account_id,
        project,
        appid,
        book_id,
        chapter_num,
        name,
        media_id,
        postback_rule_id,
        recharge_panel_id,
        advertiser_account_id
        FROM create_promotion_link
        ORDER BY id ASC");
    $stmt->execute();
    $param_rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 验证查询结果
    if (empty($param_rows)) {
        $pdo = null; // 关闭数据库连接
        die(json_encode([
            'code' => -3,
            'msg' => '未找到推广链接参数记录',
            'request_id' => uniqid('req_', true)
        ]));
    }

    error_log("找到 " . count($param_rows) . " 条待处理的推广链接记录");

    // 批量处理每条记录
    $successCount = 0;
    $failCount = 0;
    $results = [];
    $errors = [];

    foreach ($param_rows as $param_row) {
        try {
            error_log("处理记录 ID: " . $param_row['id'] . ", 名称: " . $param_row['name']);

            // 设置API请求参数 - 修复时间戳问题
            $params = [
                'admin_account_name' => $param_row['admin_account_name'],
                'account_id' => $param_row['account_id'],
                'project' => $param_row['project'],
                'appid' => $param_row['appid'],
                'book_id' => $param_row['book_id'],
                'chapter_num' => $param_row['chapter_num'],
                'name' => $param_row['name'],
                'media_id' => $param_row['media_id'],
                'postback_rule_id' => $param_row['postback_rule_id'],
                'recharge_panel_id' => $param_row['recharge_panel_id'],
                'access_key' => ACCESS_KEY,
                'random' => uniqid(),
                'timestamp' => time(),
                'is_platform_postback' => true
            ];

            // 参数验证
            $requiredParams = ['admin_account_name', 'account_id', 'project', 'appid', 'book_id', 'chapter_num', 'name', 'media_id', 'recharge_panel_id', 'access_key', 'random', 'timestamp', 'is_platform_postback'];
            foreach ($requiredParams as $requiredParam) {
                if (!isset($params[$requiredParam]) || $params[$requiredParam] === '') {
                    throw new Exception("缺少必要参数: $requiredParam");
                }
            }

            // 构造完整的API请求URL
            $api_url = API_BASE_URL . '/mapi/v2/promotion-link/create';

            // 生成签名（包含timestamp）
            $sign = generate_signature($params, SECRET_KEY);
            $params['sign'] = $sign;

            // 新增签名调试日志
            error_log("Generated Sign: {$sign} for record ID: " . $param_row['id']);

            // 尝试使用GET请求 - 修复时间戳问题
            $queryString = http_build_query($params);
            $fullApiUrl = $api_url . '?' . $queryString;

            $ch = curl_init($fullApiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

            error_log("发送GET请求: " . $fullApiUrl);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            // 解析JSON响应
            $result = json_decode($response, true);

            // 错误处理
            if ($result === null || !isset($result['code'])) {
                throw new Exception("API请求失败，HTTP状态码: $httpCode，响应: " . substr($response, 0, 200));
            }

            if ($result['code'] !== 0) {
                throw new Exception("API返回错误: " . ($result['msg'] ?? '未知错误'));
            }

            // 构建返回数据
            $linkData = [
                'id' => $result['data']['id'] ?? null,
                'link' => $result['data']['link'] ?? null,
                'ad_monitor_click_link' => $result['data']['ad_monitor_click_link'] ?? null
            ];

            // 根据 appid 查询 recharge_templates 表获取 app_name
            $appid = $param_row['appid'];
            $stmt = $pdo->prepare("SELECT app_name FROM recharge_templates WHERE appid = :appid LIMIT 1");
            $stmt->execute([':appid' => $appid]);
            $template_row = $stmt->fetch(PDO::FETCH_ASSOC);

            $app_name = ($template_row && isset($template_row['app_name']))
                ? $template_row['app_name']
                : 'unknown_app';

            // 插入到link表
            if (!empty($linkData['link'])) {
                $stmt = $pdo->prepare("
                    INSERT INTO link (advertiser_account_id, app_name, appid, link, name)
                    VALUES (:advertiser_account_id, :app_name, :appid, :link, :name)
                ");
                $stmt->execute([
                    ':advertiser_account_id' => $param_row['advertiser_account_id'],
                    ':app_name' => $app_name,
                    ':appid' => $appid,
                    ':link' => $linkData['link'],
                    ':name' => $param_row['name']
                ]);

                error_log("成功插入链接数据: ID=" . $param_row['id'] . ", 链接=" . $linkData['link']);
            }

            // 记录成功结果
            $results[] = [
                'record_id' => $param_row['id'],
                'name' => $param_row['name'],
                'link_data' => $linkData,
                'status' => 'success'
            ];
            $successCount++;

        } catch (Exception $e) {
            error_log("处理记录 ID " . $param_row['id'] . " 失败: " . $e->getMessage());
            $errors[] = [
                'record_id' => $param_row['id'],
                'name' => $param_row['name'],
                'error' => $e->getMessage(),
                'status' => 'failed'
            ];
            $failCount++;
        }

        // 添加请求间隔，避免频率限制
        if (count($param_rows) > 1) {
            sleep(1); // 每个请求间隔1秒
        }
    }

    // 构建最终返回数据
    $return_data = [
        'code' => 0,
        'msg' => 'ok',
        'data' => [
            'total_processed' => count($param_rows),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results,
            'errors' => $errors
        ],
        'request_id' => uniqid('req_', true)
    ];

    echo json_encode($return_data, JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    error_log("Unexpected error: " . $e->getMessage());
    die(json_encode([
        'code' => -1,
        'msg' => '系统错误: ' . $e->getMessage(),
        'request_id' => uniqid('req_', true)
    ]));
} finally {
    $pdo = null; // 关闭数据库连接
}

// 修改签名生成函数，适配七猫小说签名规则
function generate_signature($params, $secret_key) {
    try {
        // 移除sign参数（如果存在）
        $signParams = $params;
        unset($signParams['sign']);

        // 过滤空值参数并按参数名排序
        $filteredParams = array_filter($signParams, function($value) {
            return $value !== null && $value !== '';
        });

        // 按参数名升序排序
        ksort($filteredParams);

        // 生成待签名字符串
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= "{$key}={$value}&";
        }
        // 移除末尾的&
        $signStr = rtrim($signStr, '&');

        // 拼接secret_key并生成MD5签名
        $signStrWithSecret = $signStr . $secret_key;
        $sign = strtolower(md5($signStrWithSecret));

        // 新增调试日志输出完整签名字符串
        error_log("Sign String: {$signStrWithSecret}");
        error_log("Sign Value: {$sign}");

        return $sign;
    } catch (Exception $e) {
        error_log("Sign Generation Error: " . $e->getMessage());
        // 降级处理：返回基础签名
        return strtolower(md5($params['access_key'] . $secret_key . $params['timestamp']));
    }
}
