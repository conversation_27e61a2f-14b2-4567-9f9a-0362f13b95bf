<?php
// 在文件顶部新增强制UTF-8编码声明
header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
require_once '../config.php';

// 检查数据库配置是否定义
if (!defined('DB_HOST') || !defined('DB_USER') || !defined('DB_PASSWORD') || !defined('DB_NAME')) {
    die(json_encode([
        'code' => -4,
        'msg' => '数据库配置未定义，请检查 config.php 文件',
        'request_id' => uniqid('req_', true)
    ]));
}

// 检查 ACCESS_KEY 是否定义
if (!defined('ACCESS_KEY')) {
    die(json_encode([
        'code' => -4,
        'msg' => 'ACCESS_KEY 未定义，请检查 config.php 文件',
        'request_id' => uniqid('req_', true)
    ]));
}

// 初始化PDO连接
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    error_log("Database Connection Error: " . $e->getMessage());
    die(json_encode([
        'code' => -1,
        'msg' => '数据库连接失败: ' . $e->getMessage(),
        'request_id' => uniqid('req_', true)
    ]));
}

// 查询表中参数
try {
    $stmt = $pdo->prepare("SELECT 
        admin_account_name, 
        account_id, 
        project, 
        appid, 
        book_id, 
        chapter_num, 
        name, 
        media_id, 
        postback_rule_id, 
        recharge_panel_id, 
        advertiser_account_id  -- 新增 advertiser_account_id 字段
        FROM create_promotion_link LIMIT 1");
    $stmt->execute();
    $param_row = $stmt->fetch(PDO::FETCH_ASSOC);

    // 验证查询结果
    if (!$param_row) {
        $pdo = null; // 关闭数据库连接
        die(json_encode([
            'code' => -3,
            'msg' => '未找到推广链接参数记录',
            'request_id' => uniqid('req_', true)
        ]));
    }

    // 设置API请求参数
    $params = [
        'admin_account_name' => $param_row['admin_account_name'],
        'account_id' => $param_row['account_id'],
        'project' => $param_row['project'],
        'appid' => $param_row['appid'],
        'book_id' => $param_row['book_id'],
        'chapter_num' => $param_row['chapter_num'],
        'name' => $param_row['name'],
        'media_id' => $param_row['media_id'],
        'postback_rule_id' => $param_row['postback_rule_id'],
        'recharge_panel_id' => $param_row['recharge_panel_id'],
        'access_key' => ACCESS_KEY,
        'random' => uniqid(),
        'timestamp' => time(), // 新增时间戳参数
        'is_platform_postback' => true // 默认传入 is_platform_postback 为 true
    ];

    // 参数类型验证
    if (!isset($params['admin_account_name']) || !isset($params['account_id']) || !isset($params['project']) || !isset($params['appid']) || !isset($params['book_id']) || !isset($params['chapter_num']) || !isset($params['name']) || !isset($params['media_id']) || !isset($params['recharge_panel_id']) || !isset($params['access_key']) || !isset($params['random']) || !isset($params['timestamp']) || !isset($params['is_platform_postback'])) {
        $pdo = null; // 关闭数据库连接
        die(json_encode([
            'code' => -5,
            'msg' => '缺少必要参数',
            'request_id' => uniqid('req_', true)
        ]));
    }

    // 构造完整的API请求URL
    $api_url = API_BASE_URL . '/mapi/v2/promotion-link/create';

    // 添加时间戳和签名
    $timestamp = time();
    $sign = generate_signature($params, SECRET_KEY);

    // 完整的请求URL
    $full_api_url = $api_url . '?' . http_build_query($params) . '&timestamp=' . $timestamp . '&sign=' . $sign;

    // 新增签名调试日志
    error_log("Generated Sign: {$sign} with params: " . json_encode($params));

    // 发送POST请求
    $ch = curl_init($full_api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    $response = curl_exec($ch);
    curl_close($ch);

    // 解析JSON响应
    $result = json_decode($response, true);

    // 错误处理
    if ($result === null || !isset($result['code'])) {
        error_log("API request failed with response: " . json_encode($response));
        $return_data = [
            'code' => -1,
            'msg' => 'API请求失败',
            'data' => null,
            'request_id' => uniqid('req_', true)
        ];
        echo json_encode($return_data);
        exit;
    }

    if ($result['code'] !== 0) {
        error_log("API request failed with error: " . $result['msg']);
        $return_data = [
            'code' => $result['code'],
            'msg' => $result['msg'],
            'data' => null,
            'request_id' => uniqid('req_', true)
        ];
        echo json_encode($return_data);
        exit;
    }

    // 构建返回数据
    $data = [
        'id' => isset($result['data']['id']) ? $result['data']['id'] : null,
        'link' => isset($result['data']['link']) ? $result['data']['link'] : null,
        'ad_monitor_click_link' => isset($result['data']['ad_monitor_click_link']) ? $result['data']['ad_monitor_click_link'] : null
    ];

    // 新增：根据 appid 查询 recharge_templates 表获取 app_name
    try {
        $appid = $param_row['appid'] ?? '';
        $stmt = $pdo->prepare("SELECT app_name FROM recharge_templates WHERE appid = :appid LIMIT 1");
        $stmt->execute([':appid' => $appid]);
        $template_row = $stmt->fetch(PDO::FETCH_ASSOC);

        // 如果查询到 app_name，则更新 app_name
        if ($template_row && isset($template_row['app_name'])) {
            $app_name = $template_row['app_name'];
        } else {
            // 如果未查询到，使用原有的 app_name
            $app_name = $param_row['project'] ?? '';
        }
    } catch (Exception $e) {
        error_log("Error querying recharge_templates: " . $e->getMessage());
        // 出错时使用原有的 app_name
        $app_name = $param_row['project'] ?? '';
    }

    // 插入到link表
    try {
        $link = $data['link'] ?? '';
        // 修正为从 create_promotion_link 表中获取 advertiser_account_id
        $advertiser_account_id = $param_row['advertiser_account_id'] ?? 0; // 确保从查询结果中获取
        $appid = $param_row['appid'] ?? '';
        $name = $param_row['name'] ?? '';

        $stmt = $pdo->prepare("
            INSERT INTO link (advertiser_account_id, app_name, appid, link, name)
            VALUES (:advertiser_account_id, :app_name, :appid, :link, :name)
        ");
        $stmt->execute([
            ':advertiser_account_id' => $advertiser_account_id,
            ':app_name' => $app_name,
            ':appid' => $appid,
            ':link' => $link,
            ':name' => $name
        ]);

        error_log("Link data inserted into table: advertiser_account_id={$advertiser_account_id}, app_name={$app_name}, appid={$appid}, link={$link}, name={$name}");
    } catch (Exception $e) {
        error_log("Error inserting link data: " . $e->getMessage());
    }

    // 修改 msg 字段值为 "ok"
    $return_data = [
        'code' => 0,
        'msg' => 'ok',
        'data' => $data,
        'request_id' => uniqid('req_', true)
    ];

    echo json_encode($return_data);
} catch (Exception $e) {
    error_log("Unexpected error: " . $e->getMessage());
    die(json_encode([
        'code' => -1,
        'msg' => '系统错误: ' . $e->getMessage(),
        'request_id' => uniqid('req_', true)
    ]));
} finally {
    $pdo = null; // 关闭数据库连接
}

// 修改签名生成函数，适配七猫小说签名规则
function generate_signature($params, $secret_key) {
    try {
        // 过滤空值参数并按参数名排序
        $filteredParams = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });

        // 按参数名升序排序
        ksort($filteredParams);

        // 生成待签名字符串
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= "{$key}={$value}&";
        }
        // 移除末尾的&
        $signStr = rtrim($signStr, '&');

        // 拼接secret_key并生成MD5签名
        $signStrWithSecret = $signStr . $secret_key;
        $sign = strtolower(md5($signStrWithSecret));

        // 新增调试日志输出完整签名字符串
        error_log("Sign String: {$signStrWithSecret}");
        error_log("Sign Value: {$sign}");

        return $sign;
    } catch (Exception $e) {
        error_log("Sign Generation Error: " . $e->getMessage());
        // 降级处理：返回基础签名
        return strtolower(md5($params['access_key'] . $secret_key . $params['timestamp']));
    }
}
