<?php
// batch_create_promotion_links.php - 批量创建推广链接（1.5秒间隔）ok
header('Content-Type: application/json; charset=utf-8');

require_once '../config.php';

// 检查数据库配置
if (!defined('DB_HOST') || !defined('DB_USER') || !defined('DB_PASSWORD') || !defined('DB_NAME')) {
    die(json_encode([
        'code' => -4,
        'msg' => '数据库配置未定义，请检查 config.php 文件',
        'request_id' => uniqid('batch_', true)
    ]));
}

if (!defined('ACCESS_KEY') || !defined('SECRET_KEY') || !defined('API_BASE_URL')) {
    die(json_encode([
        'code' => -4,
        'msg' => 'API配置未定义，请检查 config.php 文件',
        'request_id' => uniqid('batch_', true)
    ]));
}

// 初始化数据库连接
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die(json_encode([
        'code' => -1,
        'msg' => '数据库连接失败: ' . $e->getMessage(),
        'request_id' => uniqid('batch_', true)
    ]));
}

// 签名生成函数（根据接口文档）
function generate_signature($params, $secret_key) {
    // 移除sign参数（如果存在）
    $signParams = $params;
    unset($signParams['sign']);

    // 过滤空值参数并按参数名排序
    $filteredParams = array_filter($signParams, function($value) {
        return $value !== null && $value !== '';
    });

    // 按参数名升序排序
    ksort($filteredParams);

    // 生成待签名字符串
    $signStr = '';
    foreach ($filteredParams as $key => $value) {
        $signStr .= "{$key}={$value}&";
    }
    // 移除末尾的&
    $signStr = rtrim($signStr, '&');

    // 拼接secret_key并生成MD5签名
    $signStrWithSecret = $signStr . $secret_key;
    $sign = strtolower(md5($signStrWithSecret));

    error_log("Sign String: {$signStrWithSecret}");
    error_log("Sign Value: {$sign}");

    return $sign;
}

// 创建单条推广链接
function create_single_promotion_link($param_row, $pdo) {
    try {
        error_log("处理记录 ID: " . $param_row['id'] . ", 名称: " . $param_row['name']);

        // 设置API请求参数
        $params = [
            'admin_account_name' => $param_row['admin_account_name'],
            'account_id' => $param_row['account_id'],
            'project' => $param_row['project'],
            'appid' => $param_row['appid'],
            'book_id' => $param_row['book_id'],
            'chapter_num' => $param_row['chapter_num'],
            'name' => $param_row['name'],
            'media_id' => $param_row['media_id'],
            'postback_rule_id' => $param_row['postback_rule_id'],
            'recharge_panel_id' => $param_row['recharge_panel_id'],
            'access_key' => ACCESS_KEY,
            'random' => uniqid(),
            'timestamp' => time(),
            'is_platform_postback' => true
        ];

        // 参数验证
        $requiredParams = ['admin_account_name', 'account_id', 'project', 'appid', 'book_id', 'chapter_num', 'name', 'media_id', 'recharge_panel_id', 'access_key', 'random', 'timestamp', 'is_platform_postback'];
        foreach ($requiredParams as $requiredParam) {
            if (!isset($params[$requiredParam]) || $params[$requiredParam] === '') {
                throw new Exception("缺少必要参数: $requiredParam");
            }
        }

        // 生成签名
        $sign = generate_signature($params, SECRET_KEY);
        $params['sign'] = $sign;

        // 构造API请求URL（使用与原始版本相同的POST请求）
        $api_url = API_BASE_URL . '/mapi/v2/promotion-link/create';

        // 添加时间戳和签名（与原始版本保持一致）
        $timestamp = time();
        $full_api_url = $api_url . '?' . http_build_query($params) . '&timestamp=' . $timestamp . '&sign=' . $sign;

        error_log("发送POST请求: " . $full_api_url);

        // 发送POST请求（与原始版本保持一致）
        $ch = curl_init($full_api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // 解析响应
        $result = json_decode($response, true);

        if ($result === null || !isset($result['code'])) {
            throw new Exception("API请求失败，HTTP状态码: $httpCode，响应: " . substr($response, 0, 200));
        }

        if ($result['code'] !== 0) {
            // 特殊处理频率限制错误
            if (isset($result['msg']) && strpos($result['msg'], '请求被限流') !== false) {
                throw new Exception("API频率限制: " . $result['msg'] . " (建议增加间隔时间)");
            }
            throw new Exception("API返回错误: " . ($result['msg'] ?? '未知错误'));
        }

        // 构建返回数据
        $linkData = [
            'id' => $result['data']['id'] ?? null,
            'link' => $result['data']['link'] ?? null,
            'ad_monitor_click_link' => $result['data']['ad_monitor_click_link'] ?? null
        ];

        // 查询app_name
        $appid = $param_row['appid'];
        $stmt = $pdo->prepare("SELECT app_name FROM recharge_templates WHERE appid = :appid LIMIT 1");
        $stmt->execute([':appid' => $appid]);
        $template_row = $stmt->fetch(PDO::FETCH_ASSOC);

        $app_name = ($template_row && isset($template_row['app_name']))
            ? $template_row['app_name']
            : 'unknown_app';

        // 插入到link表
        if (!empty($linkData['link'])) {
            $stmt = $pdo->prepare("
                INSERT INTO link (advertiser_account_id, app_name, appid, link, name)
                VALUES (:advertiser_account_id, :app_name, :appid, :link, :name)
            ");
            $stmt->execute([
                ':advertiser_account_id' => $param_row['advertiser_account_id'],
                ':app_name' => $app_name,
                ':appid' => $appid,
                ':link' => $linkData['link'],
                ':name' => $param_row['name']
            ]);

            error_log("成功插入链接数据: ID=" . $param_row['id'] . ", 链接=" . $linkData['link']);
        }

        // 处理成功后删除该记录
        $deleteStmt = $pdo->prepare("DELETE FROM create_promotion_link WHERE id = :id");
        $deleteStmt->execute([':id' => $param_row['id']]);
        error_log("已删除处理完成的记录 ID: " . $param_row['id']);

        return [
            'record_id' => $param_row['id'],
            'name' => $param_row['name'],
            'link_data' => $linkData,
            'status' => 'success'
        ];

    } catch (Exception $e) {
        error_log("处理记录 ID " . $param_row['id'] . " 失败: " . $e->getMessage());
        return [
            'record_id' => $param_row['id'],
            'name' => $param_row['name'],
            'error' => $e->getMessage(),
            'status' => 'failed'
        ];
    }
}

// 主处理逻辑
try {
    // 检查待处理的记录数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $totalRecords = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    if ($totalRecords == 0) {
        echo json_encode([
            'code' => -3,
            'msg' => '没有待处理的推广链接记录',
            'data' => [
                'total_records' => 0,
                'processed' => 0,
                'success' => 0,
                'failed' => 0
            ],
            'request_id' => uniqid('batch_', true)
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    error_log("批量处理开始，共 $totalRecords 条记录");

    $processedCount = 0;
    $successCount = 0;
    $failedCount = 0;
    $results = [];
    $errors = [];
    $startTime = microtime(true);

    // 循环处理，直到没有记录为止
    while (true) {
        // 获取下一条待处理记录
        $stmt = $pdo->prepare("SELECT
            id, admin_account_name, account_id, project, appid,
            book_id, chapter_num, name, media_id, postback_rule_id,
            recharge_panel_id, advertiser_account_id
            FROM create_promotion_link
            ORDER BY id ASC
            LIMIT 1");
        $stmt->execute();
        $param_row = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$param_row) {
            error_log("所有记录处理完成");
            break;
        }

        // 处理单条记录
        $result = create_single_promotion_link($param_row, $pdo);
        $processedCount++;

        if ($result['status'] === 'success') {
            $successCount++;
            $results[] = $result;
        } else {
            $failedCount++;
            $errors[] = $result;
        }

        // 检查是否还有更多记录
        $stmt = $pdo->prepare("SELECT COUNT(*) as remaining FROM create_promotion_link");
        $stmt->execute();
        $remaining = $stmt->fetch(PDO::FETCH_ASSOC)['remaining'];

        // 如果还有记录，等待1.5秒再处理下一条
        if ($remaining > 0) {
            error_log("等待1.5秒后处理下一条记录...");
            usleep(1500000); // 1.5秒间隔
        }

        // 安全检查：避免无限循环
        if ($processedCount >= $totalRecords + 10) {
            error_log("安全检查：处理次数超过预期，停止处理");
            break;
        }
    }

    $endTime = microtime(true);
    $totalTime = round($endTime - $startTime, 2);

    // 构建最终返回数据
    $return_data = [
        'code' => 0,
        'msg' => 'ok',
        'data' => [
            'total_records' => $totalRecords,
            'processed' => $processedCount,
            'success' => $successCount,
            'failed' => $failedCount,
            'execution_time' => $totalTime,
            'average_time_per_record' => $processedCount > 0 ? round($totalTime / $processedCount, 2) : 0,
            'results' => $results,
            'errors' => $errors
        ],
        'request_id' => uniqid('batch_', true)
    ];

    error_log("批量处理完成: 总数=$totalRecords, 处理=$processedCount, 成功=$successCount, 失败=$failedCount, 耗时={$totalTime}秒");

    echo json_encode($return_data, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    error_log("批量处理错误: " . $e->getMessage());
    echo json_encode([
        'code' => -1,
        'msg' => '批量处理错误: ' . $e->getMessage(),
        'data' => null,
        'request_id' => uniqid('batch_', true)
    ], JSON_UNESCAPED_UNICODE);
}
?>
