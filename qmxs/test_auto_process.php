<?php
// test_auto_process.php - 测试自动处理流程

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>测试自动处理流程</title></head>";
echo "<body>";
echo "<h1>测试自动处理流程</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查当前状态
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $createCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<h2>当前状态</h2>";
    echo "<p><strong>create_promotion_link表:</strong> $createCount 条待处理记录</p>";
    
    if ($createCount > 0) {
        // 显示待处理记录
        $stmt = $pdo->prepare("SELECT id, name, advertiser_account_id FROM create_promotion_link ORDER BY id ASC");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>待处理记录:</h3>";
        echo "<table border='1' style='border-collapse: collapse; font-size: 12px;'>";
        echo "<tr><th>ID</th><th>名称</th><th>广告主账户ID</th></tr>";
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . htmlspecialchars($record['advertiser_account_id']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (isset($_POST['auto_process'])) {
            echo "<h2>开始自动处理</h2>";
            
            // 调用批量处理
            echo "<p>调用批量处理API...</p>";
            $startTime = microtime(true);
            
            // 使用相对路径直接包含文件
            ob_start();
            include_once __DIR__ . '/modules/batch_create_promotion_links.php';
            $batchResponse = ob_get_clean();
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
            echo "<p><strong>响应长度:</strong> " . strlen($batchResponse) . " 字节</p>";
            
            if ($batchResponse) {
                $batchResult = json_decode($batchResponse, true);
                if ($batchResult && $batchResult['code'] === 0) {
                    echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 批量处理成功！</h4>";
                    echo "<ul style='margin: 0;'>";
                    echo "<li><strong>总记录数:</strong> " . $batchResult['data']['total_records'] . "</li>";
                    echo "<li><strong>处理数量:</strong> " . $batchResult['data']['processed'] . "</li>";
                    echo "<li><strong>成功数量:</strong> " . $batchResult['data']['success'] . "</li>";
                    echo "<li><strong>失败数量:</strong> " . $batchResult['data']['failed'] . "</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                    if ($batchResult['data']['success'] > 0) {
                        // 生成Excel文件
                        echo "<h3>生成Excel文件</h3>";
                        
                        // 创建下载链接
                        $excelData = base64_encode(json_encode($batchResult['data']));
                        echo "<form method='post' action='download_excel.php' target='_blank' style='margin: 20px 0;'>";
                        echo "<input type='hidden' name='excel_data' value='$excelData'>";
                        echo "<button type='submit' style='padding: 15px 30px; background-color: #28a745; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>📊 下载Excel文件</button>";
                        echo "</form>";
                        
                        // 显示成功的链接
                        echo "<h4>✅ 成功创建的推广链接:</h4>";
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
                        echo "<tr style='background-color: #CCE5FF; font-weight: bold;'>";
                        echo "<th>广告主账户ID</th><th>小程序名称</th><th>小程序原始ID</th><th>推广链接</th><th>链接名称</th>";
                        echo "</tr>";
                        
                        foreach ($batchResult['data']['results'] as $success) {
                            $linkData = $success['link_data'];
                            echo "<tr>";
                            echo "<td>555666777</td>"; // 示例广告主账户ID
                            echo "<td>七猫小说</td>";
                            echo "<td>wxe3a874175a6e6ed3</td>";
                            echo "<td><a href='" . htmlspecialchars($linkData['link'] ?? '') . "' target='_blank'>" . htmlspecialchars($linkData['link'] ?? '') . "</a></td>";
                            echo "<td>" . htmlspecialchars($success['name']) . "</td>";
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                    
                    if (!empty($batchResult['data']['errors'])) {
                        echo "<h4>❌ 处理失败的记录:</h4>";
                        echo "<ul>";
                        foreach ($batchResult['data']['errors'] as $error) {
                            echo "<li style='color: red;'>" . htmlspecialchars($error['name']) . ": " . htmlspecialchars($error['error']) . "</li>";
                        }
                        echo "</ul>";
                    }
                    
                } else {
                    echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4 style='color: #721c24; margin: 0;'>❌ 批量处理失败</h4>";
                    echo "<p style='margin: 10px 0 0 0;'><strong>错误:</strong> " . htmlspecialchars($batchResult['msg'] ?? '未知错误') . "</p>";
                    echo "</div>";
                    
                    echo "<details style='margin: 20px 0;'>";
                    echo "<summary style='cursor: pointer; font-weight: bold;'>查看完整响应</summary>";
                    echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
                    echo htmlspecialchars($batchResponse);
                    echo "</pre>";
                    echo "</details>";
                }
            } else {
                echo "<p style='color: red;'>❌ 批量处理无响应</p>";
            }
            
            // 检查最终状态
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
            $stmt->execute();
            $finalCreateCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link WHERE name LIKE '%测试%' OR name LIKE '%提交%'");
            $stmt->execute();
            $linkCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<h3>处理后状态</h3>";
            echo "<p><strong>create_promotion_link表:</strong> $finalCreateCount 条记录</p>";
            echo "<p><strong>link表中测试记录:</strong> $linkCount 条记录</p>";
            
        } else {
            echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>🚀 自动处理测试</h3>";
            echo "<p>此测试将模拟用户提交后的自动处理流程：</p>";
            echo "<ol>";
            echo "<li>调用批量处理API处理待处理记录</li>";
            echo "<li>生成推广链接并存储到link表</li>";
            echo "<li>生成Excel文件供下载</li>";
            echo "</ol>";
            echo "<form method='post'>";
            echo "<button type='submit' name='auto_process' style='padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>🚀 开始自动处理</button>";
            echo "</form>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ 没有待处理的记录</p>";
        echo "<p><a href='add_test_data.php' style='padding: 10px 20px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 5px;'>添加测试数据</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
