<?php
// test_create_api.php - 测试批量创建推广链接API

// 设置内部编码
mb_internal_encoding('UTF-8');
header('Content-Type: application/json; charset=UTF-8');

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查 create_promotion_link 表数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo json_encode([
        'status' => 'info',
        'message' => "create_promotion_link 表中有 $count 条记录",
        'record_count' => $count
    ], JSON_UNESCAPED_UNICODE) . "\n";
    
    if ($count === 0) {
        echo json_encode([
            'status' => 'warning',
            'message' => '没有待处理的记录，请先添加推广链接参数'
        ], JSON_UNESCAPED_UNICODE) . "\n";
        exit;
    }
    
    // 显示前几条记录
    $stmt = $pdo->prepare("SELECT id, name, appid, book_id, chapter_num FROM create_promotion_link ORDER BY id ASC LIMIT 3");
    $stmt->execute();
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'info',
        'message' => '前3条记录预览',
        'records' => $records
    ], JSON_UNESCAPED_UNICODE) . "\n";
    
    // 调用批量创建API
    echo json_encode([
        'status' => 'info',
        'message' => '开始调用批量创建API...'
    ], JSON_UNESCAPED_UNICODE) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/create_promotion_link.php");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    if ($curlError) {
        echo json_encode([
            'status' => 'error',
            'message' => 'CURL错误: ' . $curlError
        ], JSON_UNESCAPED_UNICODE) . "\n";
        exit;
    }
    
    echo json_encode([
        'status' => 'info',
        'message' => "API调用完成，HTTP状态码: $httpCode"
    ], JSON_UNESCAPED_UNICODE) . "\n";
    
    if ($response) {
        $result = json_decode($response, true);
        if ($result) {
            echo json_encode([
                'status' => 'success',
                'message' => 'API响应解析成功',
                'api_response' => $result
            ], JSON_UNESCAPED_UNICODE) . "\n";
            
            // 检查处理结果
            if ($result['code'] === 0) {
                $data = $result['data'];
                echo json_encode([
                    'status' => 'success',
                    'message' => '批量处理完成',
                    'summary' => [
                        'total_processed' => $data['total_processed'],
                        'success_count' => $data['success_count'],
                        'fail_count' => $data['fail_count']
                    ]
                ], JSON_UNESCAPED_UNICODE) . "\n";
                
                // 显示成功的记录
                if (!empty($data['results'])) {
                    echo json_encode([
                        'status' => 'info',
                        'message' => '成功创建的链接',
                        'successful_links' => array_slice($data['results'], 0, 3) // 只显示前3个
                    ], JSON_UNESCAPED_UNICODE) . "\n";
                }
                
                // 显示错误的记录
                if (!empty($data['errors'])) {
                    echo json_encode([
                        'status' => 'warning',
                        'message' => '处理失败的记录',
                        'errors' => $data['errors']
                    ], JSON_UNESCAPED_UNICODE) . "\n";
                }
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'API返回错误: ' . $result['msg'],
                    'error_code' => $result['code']
                ], JSON_UNESCAPED_UNICODE) . "\n";
            }
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'API响应解析失败',
                'raw_response' => substr($response, 0, 500)
            ], JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'API无响应'
        ], JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    // 检查 link 表的变化
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link");
    $stmt->execute();
    $linkCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo json_encode([
        'status' => 'info',
        'message' => "link 表中现在有 $linkCount 条记录"
    ], JSON_UNESCAPED_UNICODE) . "\n";
    
    if ($linkCount > 0) {
        $stmt = $pdo->prepare("SELECT id, name, app_name, link, created_at FROM link ORDER BY id DESC LIMIT 3");
        $stmt->execute();
        $linkRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'status' => 'info',
            'message' => '最新的链接记录',
            'latest_links' => $linkRecords
        ], JSON_UNESCAPED_UNICODE) . "\n";
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '异常: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE) . "\n";
}
?>
