<?php
// test_complete_flow.php - 测试完整流程：插入数据 -> 批量处理 -> Excel导出

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>测试完整流程</title></head>";
echo "<body>";
echo "<h1>测试完整流程</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if (isset($_POST['test_flow'])) {
        echo "<h2>开始测试完整流程</h2>";
        
        // 1. 清空现有数据
        echo "<p>1. 清空现有测试数据...</p>";
        $pdo->exec("DELETE FROM create_promotion_link WHERE name LIKE '测试链接%'");
        
        // 2. 插入测试数据
        echo "<p>2. 插入测试数据...</p>";
        $testData = [
            ['测试链接A', '*********', '515208', '1'],
            ['测试链接B', '*********', '515208', '2'],
            ['测试链接C', '*********', '515208', '3']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO create_promotion_link (
            admin_account_name, account_id, project, appid,
            book_id, chapter_num, name, media_id,
            postback_rule_id, recharge_panel_id, advertiser_account_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($testData as $data) {
            $stmt->execute([
                'aishang', // admin_account_name
                553456211129746234, // account_id
                8, // project
                'wxe3a874175a6e6ed3', // appid
                $data[2], // book_id
                $data[3], // chapter_num
                $data[0], // name
                1, // media_id
                6505, // postback_rule_id
                5501, // recharge_panel_id
                $data[1] // advertiser_account_id
            ]);
        }
        
        echo "<p>✅ 成功插入 " . count($testData) . " 条测试数据</p>";
        
        // 3. 调用批量处理
        echo "<p>3. 调用批量处理API...</p>";
        $startTime = microtime(true);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/batch_create_promotion_links.php");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        
        echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
        echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
        
        if ($response && $httpCode == 200) {
            $result = json_decode($response, true);
            if ($result && $result['code'] === 0) {
                echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 批量处理成功！</h4>";
                echo "<ul style='margin: 0;'>";
                echo "<li><strong>总记录数:</strong> " . $result['data']['total_records'] . "</li>";
                echo "<li><strong>处理数量:</strong> " . $result['data']['processed'] . "</li>";
                echo "<li><strong>成功数量:</strong> " . $result['data']['success'] . "</li>";
                echo "<li><strong>失败数量:</strong> " . $result['data']['failed'] . "</li>";
                echo "</ul>";
                echo "</div>";
                
                // 4. 生成Excel文件
                echo "<p>4. 生成Excel文件...</p>";
                
                // 创建下载链接
                $excelData = base64_encode(json_encode($result['data']));
                echo "<form method='post' action='download_excel.php' target='_blank'>";
                echo "<input type='hidden' name='excel_data' value='$excelData'>";
                echo "<button type='submit' style='padding: 15px 30px; background-color: #28a745; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>📊 下载Excel文件</button>";
                echo "</form>";
                
                // 显示成功的链接
                if (!empty($result['data']['results'])) {
                    echo "<h4>✅ 成功创建的推广链接:</h4>";
                    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
                    echo "<tr><th>序号</th><th>链接名称</th><th>推广链接ID</th><th>推广链接</th></tr>";
                    
                    foreach ($result['data']['results'] as $index => $success) {
                        echo "<tr>";
                        echo "<td>" . ($index + 1) . "</td>";
                        echo "<td>" . htmlspecialchars($success['name']) . "</td>";
                        echo "<td>" . htmlspecialchars($success['link_data']['id'] ?? '') . "</td>";
                        echo "<td><a href='" . htmlspecialchars($success['link_data']['link'] ?? '') . "' target='_blank'>" . htmlspecialchars($success['link_data']['link'] ?? '') . "</a></td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
                
                if (!empty($result['data']['errors'])) {
                    echo "<h4>❌ 处理失败的记录:</h4>";
                    echo "<ul>";
                    foreach ($result['data']['errors'] as $error) {
                        echo "<li style='color: red;'>" . htmlspecialchars($error['name']) . ": " . htmlspecialchars($error['error']) . "</li>";
                    }
                    echo "</ul>";
                }
                
            } else {
                echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: #721c24; margin: 0;'>❌ 批量处理失败</h4>";
                echo "<p style='margin: 10px 0 0 0;'><strong>错误:</strong> " . htmlspecialchars($result['msg'] ?? '未知错误') . "</p>";
                echo "</div>";
            }
        } else {
            echo "<p style='color: red;'>❌ API调用失败，HTTP状态码: $httpCode</p>";
            if ($response) {
                echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
                echo htmlspecialchars(substr($response, 0, 1000));
                echo "</pre>";
            }
        }
        
    } else {
        // 显示当前状态
        echo "<h2>当前状态检查</h2>";
        
        // 检查create_promotion_link表
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
        $stmt->execute();
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<p><strong>create_promotion_link表:</strong> $count 条待处理记录</p>";
        
        // 检查link表
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link");
        $stmt->execute();
        $linkCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<p><strong>link表:</strong> $linkCount 条推广链接记录</p>";
        
        // 显示最近的link记录
        $stmt = $pdo->prepare("SELECT * FROM link ORDER BY id DESC LIMIT 5");
        $stmt->execute();
        $recentLinks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($recentLinks)) {
            echo "<h3>最近的推广链接记录:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
            echo "<tr><th>ID</th><th>应用名称</th><th>AppID</th><th>链接名称</th><th>推广链接</th><th>广告主账户ID</th></tr>";
            
            foreach ($recentLinks as $link) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($link['id']) . "</td>";
                echo "<td>" . htmlspecialchars($link['app_name']) . "</td>";
                echo "<td>" . htmlspecialchars($link['appid']) . "</td>";
                echo "<td>" . htmlspecialchars($link['name']) . "</td>";
                echo "<td><a href='" . htmlspecialchars($link['link']) . "' target='_blank'>" . htmlspecialchars(substr($link['link'], 0, 50)) . "...</a></td>";
                echo "<td>" . htmlspecialchars($link['advertiser_account_id']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🧪 完整流程测试</h3>";
        echo "<p>此测试将模拟完整的用户操作流程：</p>";
        echo "<ol>";
        echo "<li>插入3条测试数据到 create_promotion_link 表</li>";
        echo "<li>自动调用批量处理API</li>";
        echo "<li>生成Excel文件供下载</li>";
        echo "<li>显示处理结果</li>";
        echo "</ol>";
        echo "<form method='post'>";
        echo "<button type='submit' name='test_flow' style='padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>🚀 开始完整流程测试</button>";
        echo "</form>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
