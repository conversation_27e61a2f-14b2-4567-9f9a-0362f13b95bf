<?php
// test_complete_flow.php - 完整流程测试
session_start();
require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>完整流程测试</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0; }";
echo ".error { background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0; }";
echo ".info { background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 10px 0; }";
echo "table { border-collapse: collapse; width: 100%; font-size: 12px; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
echo "th { background-color: #CCE5FF; font-weight: bold; }";
echo "button { padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px; margin: 10px 5px; }";
echo "button:hover { background-color: #0056b3; }";
echo ".download-btn { background-color: #28a745; }";
echo ".download-btn:hover { background-color: #1e7e34; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<h1>🧪 完整流程测试</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if (isset($_POST['step'])) {
        $step = $_POST['step'];
        
        if ($step === 'add_data') {
            echo "<h2>步骤1: 添加测试数据</h2>";
            
            // 清空现有测试数据
            $stmt = $pdo->prepare("DELETE FROM create_promotion_link WHERE name LIKE '%完整测试%'");
            $stmt->execute();
            
            // 插入新的测试数据
            $testData = [
                ['完整测试链接A', '*********', '515208', '1'],
                ['完整测试链接B', '*********', '515208', '2']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO create_promotion_link (
                admin_account_name, account_id, project, appid,
                book_id, chapter_num, name, media_id,
                postback_rule_id, recharge_panel_id, advertiser_account_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            $insertCount = 0;
            foreach ($testData as $data) {
                $stmt->execute([
                    'aishang',
                    553456211129746234,
                    8,
                    'wxe3a874175a6e6ed3',
                    $data[2],
                    $data[3],
                    $data[0],
                    1,
                    6505,
                    5501,
                    $data[1]
                ]);
                $insertCount++;
            }
            
            echo "<div class='success'>";
            echo "<h4>✅ 测试数据添加成功</h4>";
            echo "<p>成功插入 $insertCount 条测试记录到 create_promotion_link 表</p>";
            echo "</div>";
            
            echo "<form method='post'>";
            echo "<input type='hidden' name='step' value='process'>";
            echo "<button type='submit'>下一步: 执行批量处理</button>";
            echo "</form>";
            
        } else if ($step === 'process') {
            echo "<h2>步骤2: 执行批量处理</h2>";
            
            // 检查待处理记录
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link WHERE name LIKE '%完整测试%'");
            $stmt->execute();
            $pendingCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<p><strong>待处理记录:</strong> $pendingCount 条</p>";
            
            if ($pendingCount > 0) {
                // 调用批量处理API
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
                $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
                $apiUrl = $baseUrl . '/modules/batch_create_promotion_links.php';
                
                echo "<p>调用API: $apiUrl</p>";
                
                $startTime = microtime(true);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $apiUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 300);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curlError = curl_error($ch);
                curl_close($ch);
                $endTime = microtime(true);
                
                $executionTime = round($endTime - $startTime, 2);
                
                echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
                echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
                
                if ($curlError) {
                    echo "<div class='error'>";
                    echo "<h4>❌ CURL错误</h4>";
                    echo "<p>$curlError</p>";
                    echo "</div>";
                } else if ($httpCode == 200 && $response) {
                    $result = json_decode($response, true);
                    if ($result && $result['code'] === 0) {
                        echo "<div class='success'>";
                        echo "<h4>✅ 批量处理成功</h4>";
                        echo "<ul>";
                        echo "<li><strong>总记录数:</strong> " . $result['data']['total_records'] . "</li>";
                        echo "<li><strong>处理数量:</strong> " . $result['data']['processed'] . "</li>";
                        echo "<li><strong>成功数量:</strong> " . $result['data']['success'] . "</li>";
                        echo "<li><strong>失败数量:</strong> " . $result['data']['failed'] . "</li>";
                        echo "<li><strong>执行时间:</strong> " . $result['data']['execution_time'] . "秒</li>";
                        echo "</ul>";
                        echo "</div>";
                        
                        if ($result['data']['success'] > 0) {
                            // 存储结果用于Excel生成
                            $_SESSION['batch_result'] = $result['data'];
                            
                            echo "<form method='post'>";
                            echo "<input type='hidden' name='step' value='excel'>";
                            echo "<button type='submit'>下一步: 生成Excel文件</button>";
                            echo "</form>";
                        }
                        
                    } else {
                        echo "<div class='error'>";
                        echo "<h4>❌ 批量处理失败</h4>";
                        echo "<p><strong>错误:</strong> " . ($result['msg'] ?? '未知错误') . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<div class='error'>";
                    echo "<h4>❌ API调用失败</h4>";
                    echo "<p>HTTP状态码: $httpCode</p>";
                    echo "<p>响应长度: " . strlen($response) . " 字节</p>";
                    echo "</div>";
                }
                
            } else {
                echo "<div class='info'>";
                echo "<h4>ℹ️ 没有待处理记录</h4>";
                echo "<p>请先添加测试数据</p>";
                echo "</div>";
            }
        }
    } else {
        // 显示测试说明和开始按钮
        echo "<div class='info'>";
        echo "<h3>🎯 完整流程测试说明</h3>";
        echo "<p>此测试将验证从用户提交表单到Excel文件下载的完整流程</p>";
        echo "</div>";
        
        echo "<form method='post'>";
        echo "<input type='hidden' name='step' value='add_data'>";
        echo "<button type='submit'>🚀 开始完整流程测试</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ 错误</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
