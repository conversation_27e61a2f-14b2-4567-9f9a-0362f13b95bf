<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - 4个输入框横向排列</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group { 
            margin-bottom: 15px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
            color: #333;
        }
        input, select { 
            width: 100%; 
            padding: 8px; 
            box-sizing: border-box; 
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea { 
            width: 100%; 
            padding: 8px; 
            box-sizing: border-box; 
            resize: vertical; 
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: Arial, sans-serif;
        }
        button { 
            padding: 10px 15px; 
            background-color: #4CAF50; 
            color: white; 
            border: none; 
            cursor: pointer; 
            border-radius: 4px;
            font-size: 16px;
        }
        button:hover { 
            background-color: #45a049; 
        }
        
        /* 4个输入框横向排列的样式 */
        .textarea-row { 
            display: flex; 
            gap: 15px; 
            margin-bottom: 15px; 
        }
        .textarea-col { 
            flex: 1; 
        }
        .textarea-col label { 
            margin-bottom: 5px; 
        }
        .textarea-col textarea { 
            height: 150px; 
        }
        
        /* 响应式设计：在小屏幕上垂直排列 */
        @media (max-width: 768px) { 
            .textarea-row { 
                flex-direction: column; 
                gap: 10px; 
            } 
        }
        
        /* 计数器样式 */
        .count-span {
            color: #666;
            font-size: 12px;
            font-weight: normal;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-note {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>布局测试 - 4个输入框横向排列</h1>
        
        <div class="demo-note">
            <strong>说明：</strong>这是修改后的布局效果演示。4个输入框现在横向排列，平均分配宽度，并且支持响应式设计。
        </div>
        
        <form>
            <!-- 模拟其他表单字段 -->
            <div class="form-group">
                <label for="user">选择用户</label>
                <select id="user" name="user">
                    <option value="">请选择用户</option>
                    <option value="user1">用户1</option>
                    <option value="user2">用户2</option>
                </select>
            </div>

            <div class="form-group">
                <label for="template">选择充值模板</label>
                <select id="template" name="template">
                    <option value="">请选择模板</option>
                    <option value="template1">模板1</option>
                    <option value="template2">模板2</option>
                </select>
            </div>

            <!-- 4个输入框横向排列 -->
            <div class="textarea-row">
                <!-- 1. 链接名称 -->
                <div class="textarea-col">
                    <label for="book_names">链接名称（一行一个名称）<span id="book_names_count" class="count-span">(已输入0个名称)</span></label>
                    <textarea id="book_names" name="book_names" placeholder="请输入名称，每行一个名称，支持多行输入"></textarea>
                </div>

                <!-- 2. 广告主账户ID -->
                <div class="textarea-col">
                    <label for="advertiser_account_ids">广告主账户ID（一行一个ID）<span id="advertiser_ids_count" class="count-span">(已输入0个广告主账户ID)</span></label>
                    <textarea id="advertiser_account_ids" name="advertiser_account_ids" placeholder="请输入广告主账户ID，每行一个广告主账户ID"></textarea>
                </div>

                <!-- 3. 投放书籍ID -->
                <div class="textarea-col">
                    <label for="book_ids">投放书籍ID（一行一个ID）<span id="book_ids_count" class="count-span">(已输入0个书籍ID)</span></label>
                    <textarea id="book_ids" name="book_ids" placeholder="请输入投放书籍ID，每行一个书籍ID"></textarea>
                </div>

                <!-- 4. 投放章节序号 -->
                <div class="textarea-col">
                    <label for="chapter_numbers">投放章节序号ID（一行一个ID）<span id="chapter_numbers_count" class="count-span">(已输入0个章节序号ID)</span></label>
                    <textarea id="chapter_numbers" name="chapter_numbers" placeholder="请输入章节序号ID，默认为：1"></textarea>
                </div>
            </div>

            <div class="form-group">
                <button type="submit">提交</button>
            </div>
        </form>
    </div>

    <script>
        // 输入框计数功能
        function updateCount(textareaId, countId, label) {
            const textarea = document.getElementById(textareaId);
            const countSpan = document.getElementById(countId);
            
            function updateCounter() {
                const lines = textarea.value.split('\n').filter(line => line.trim() !== '');
                countSpan.textContent = `(已输入${lines.length}个${label})`;
            }
            
            textarea.addEventListener('input', updateCounter);
            textarea.addEventListener('paste', function() {
                setTimeout(updateCounter, 10);
            });
        }

        // 页面加载完成后初始化计数器
        document.addEventListener('DOMContentLoaded', function() {
            updateCount('book_names', 'book_names_count', '名称');
            updateCount('advertiser_account_ids', 'advertiser_ids_count', '广告主账户ID');
            updateCount('book_ids', 'book_ids_count', '书籍ID');
            updateCount('chapter_numbers', 'chapter_numbers_count', '章节序号ID');
        });
    </script>
</body>
</html>
