<?php
// test_name_insert.php - 测试中英文字符存储到name字段

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>Name字段存储测试</title></head>";
echo "<body>";
echo "<h1>Name字段中英文字符存储测试</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $testNames = $_POST['test_names'] ?? '';
    
    echo "<h2>测试数据处理</h2>";
    echo "原始输入: " . htmlspecialchars($testNames) . "<br>";
    
    // 按照主页面的逻辑处理
    $bookNames = $testNames;
    $bookNames = mb_convert_encoding($bookNames, 'UTF-8', mb_detect_encoding($bookNames, 'UTF-8, GBK, GB2312, BIG5', true));
    
    $names = array_filter(array_map(function($item) {
        $trimmed = trim($item);
        return mb_convert_encoding($trimmed, 'UTF-8', mb_detect_encoding($trimmed, 'UTF-8, GBK, GB2312, BIG5', true));
    }, explode("\n", $bookNames)));
    
    $validNames = array_filter($names, function($name) {
        return !empty($name) && mb_check_encoding($name, 'UTF-8');
    });
    
    echo "处理后的有效名称: ";
    foreach ($validNames as $i => $name) {
        echo "[$i] " . htmlspecialchars($name) . " ";
    }
    echo "<br><br>";
    
    if (!empty($validNames)) {
        try {
            $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($conn->connect_error) {
                throw new Exception("连接失败: " . $conn->connect_error);
            }
            
            // 设置字符集
            $conn->set_charset("utf8mb4");
            $conn->query("SET NAMES 'utf8mb4'");
            
            echo "<h2>数据库插入测试</h2>";
            
            // 准备插入语句（使用修复后的逻辑）
            $stmt = $conn->prepare("INSERT INTO create_promotion_link (
                admin_account_name, account_id, project, appid, 
                book_id, chapter_num, name, media_id, 
                postback_rule_id, recharge_panel_id, advertiser_account_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            if (!$stmt) {
                throw new Exception("准备语句失败: " . $conn->error);
            }
            
            $insertedIds = [];
            
            foreach ($validNames as $index => $name) {
                echo "<h3>测试 #" . ($index + 1) . ": " . htmlspecialchars($name) . "</h3>";
                
                // 设置测试数据
                $adminAccountName = "test_admin";
                $accountId = 999;
                $project = 8;
                $appId = "test_app_" . time();
                $bookId = $index + 1;
                $chapterNum = 1;
                $mediaId = 1;
                $postbackRuleId = 1;
                $rechargePanelId = 1;
                $advertiserId = "999" . $index;
                
                echo "参数类型: siisiisiiis<br>";
                echo "Name参数: " . htmlspecialchars($name) . " (长度: " . mb_strlen($name) . ", 字节: " . strlen($name) . ")<br>";
                
                // 使用修复后的bind_param
                $stmt->bind_param(
                    "siisiisiiis", // 修正后的类型字符串
                    $adminAccountName,  // s
                    $accountId,         // i
                    $project,           // i
                    $appId,             // s
                    $bookId,            // i
                    $chapterNum,        // i
                    $name,              // s - 这是关键的name字段
                    $mediaId,           // i
                    $postbackRuleId,    // i
                    $rechargePanelId,   // i
                    $advertiserId       // s
                );
                
                if ($stmt->execute()) {
                    $insertId = $conn->insert_id;
                    $insertedIds[] = $insertId;
                    echo "✅ 插入成功，ID: $insertId<br>";
                    
                    // 立即查询验证
                    $checkStmt = $conn->prepare("SELECT name FROM create_promotion_link WHERE id = ?");
                    $checkStmt->bind_param("i", $insertId);
                    $checkStmt->execute();
                    $result = $checkStmt->get_result();
                    $row = $result->fetch_assoc();
                    
                    echo "数据库中的值: " . htmlspecialchars($row['name']) . "<br>";
                    echo "存储成功: " . ($row['name'] === $name ? '✅ 是' : '❌ 否') . "<br>";
                    
                } else {
                    echo "❌ 插入失败: " . $stmt->error . "<br>";
                }
                echo "<hr>";
            }
            
            // 清理测试数据
            if (!empty($insertedIds)) {
                echo "<h2>清理测试数据</h2>";
                $placeholders = str_repeat('?,', count($insertedIds) - 1) . '?';
                $deleteStmt = $conn->prepare("DELETE FROM create_promotion_link WHERE id IN ($placeholders)");
                $types = str_repeat('i', count($insertedIds));
                $deleteStmt->bind_param($types, ...$insertedIds);
                
                if ($deleteStmt->execute()) {
                    echo "✅ 已清理 " . count($insertedIds) . " 条测试数据<br>";
                } else {
                    echo "❌ 清理测试数据失败: " . $deleteStmt->error . "<br>";
                }
            }
            
        } catch (Exception $e) {
            echo "❌ 错误: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "<h2>❌ 没有有效的名称数据</h2>";
    }
}

echo "<h2>测试表单</h2>";
echo "<form method='post' accept-charset='UTF-8'>";
echo "<label>输入测试名称（每行一个，支持中英文）：</label><br>";
echo "<textarea name='test_names' rows='5' cols='50' placeholder='请输入测试名称，每行一个&#10;例如：&#10;测试中文名称&#10;Test English Name&#10;混合Mixed名称'>" . htmlspecialchars($_POST['test_names'] ?? '') . "</textarea><br><br>";
echo "<button type='submit'>开始测试</button>";
echo "</form>";

echo "<h2>说明</h2>";
echo "<p>此测试会：</p>";
echo "<ul>";
echo "<li>使用与主页面相同的编码处理逻辑</li>";
echo "<li>使用修复后的bind_param类型字符串</li>";
echo "<li>插入测试数据并立即验证</li>";
echo "<li>自动清理测试数据</li>";
echo "</ul>";

echo "</body></html>";
?>
