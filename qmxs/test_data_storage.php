<?php
// test_data_storage.php - 测试API数据存储功能

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始测试API数据存储功能...\n\n";

// 引入配置文件
require_once 'config.php';

// 测试数据库连接
echo "1. 测试数据库连接:\n";
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }
    echo "✓ 数据库连接成功\n";
    
    // 设置字符集
    $conn->set_charset("utf8mb4");
    
    // 检查表结构
    echo "\n2. 检查表结构:\n";
    
    // 检查 recharge_templates 表的唯一键
    $result = $conn->query("SHOW INDEX FROM recharge_templates WHERE Key_name = 'unique_template'");
    if ($result && $result->num_rows > 0) {
        echo "✓ recharge_templates 表唯一键存在\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - 字段: {$row['Column_name']}, 位置: {$row['Seq_in_index']}\n";
        }
    } else {
        echo "✗ recharge_templates 表唯一键不存在或有问题\n";
    }
    
    // 检查 postback_rules 表的唯一键
    $result = $conn->query("SHOW INDEX FROM postback_rules WHERE Key_name = 'unique_postback'");
    if ($result && $result->num_rows > 0) {
        echo "✓ postback_rules 表唯一键存在\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - 字段: {$row['Column_name']}, 位置: {$row['Seq_in_index']}\n";
        }
    } else {
        echo "✗ postback_rules 表唯一键不存在或有问题\n";
    }
    
    // 统计现有数据
    echo "\n3. 统计现有数据:\n";
    
    $result = $conn->query("SELECT COUNT(*) as total FROM recharge_templates");
    $row = $result->fetch_assoc();
    echo "充值模板总数: {$row['total']}\n";
    
    $result = $conn->query("SELECT COUNT(DISTINCT account_id) as unique_accounts FROM recharge_templates");
    $row = $result->fetch_assoc();
    echo "充值模板唯一子账号数: {$row['unique_accounts']}\n";
    
    $result = $conn->query("SELECT COUNT(*) as total FROM postback_rules");
    $row = $result->fetch_assoc();
    echo "回传规则总数: {$row['total']}\n";
    
    $result = $conn->query("SELECT COUNT(DISTINCT account_id) as unique_accounts FROM postback_rules");
    $row = $result->fetch_assoc();
    echo "回传规则唯一子账号数: {$row['unique_accounts']}\n";
    
    // 检查同一子账号的多条数据
    echo "\n4. 检查同一子账号的多条数据:\n";
    
    $result = $conn->query("
        SELECT account_id, account_name, COUNT(*) as count 
        FROM recharge_templates 
        GROUP BY account_id, account_name 
        HAVING count > 1 
        ORDER BY count DESC 
        LIMIT 5
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "充值模板 - 有多条数据的子账号:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - 账号ID: {$row['account_id']}, 账号名: {$row['account_name']}, 数据条数: {$row['count']}\n";
        }
    } else {
        echo "充值模板 - 没有找到有多条数据的子账号\n";
    }
    
    $result = $conn->query("
        SELECT account_id, account_name, COUNT(*) as count 
        FROM postback_rules 
        GROUP BY account_id, account_name 
        HAVING count > 1 
        ORDER BY count DESC 
        LIMIT 5
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "回传规则 - 有多条数据的子账号:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - 账号ID: {$row['account_id']}, 账号名: {$row['account_name']}, 数据条数: {$row['count']}\n";
        }
    } else {
        echo "回传规则 - 没有找到有多条数据的子账号\n";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "✗ 错误: " . $e->getMessage() . "\n";
}

echo "\n5. 测试API调用:\n";

// 测试充值模板API
echo "测试充值模板API...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/get_recharge_templates.php?fetchAll=1");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200 && $response) {
    $data = json_decode($response, true);
    if ($data && $data['code'] === 0) {
        echo "✓ 充值模板API调用成功\n";
        echo "  - 返回数据条数: " . count($data['data']['list']) . "\n";
    } else {
        echo "✗ 充值模板API返回错误: " . ($data['msg'] ?? '未知错误') . "\n";
    }
} else {
    echo "✗ 充值模板API调用失败，HTTP状态码: $httpCode\n";
}

// 测试回传规则API
echo "测试回传规则API...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/get_return_rules.php?page_size=1000");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200 && $response) {
    $data = json_decode($response, true);
    if ($data && $data['code'] === 0) {
        echo "✓ 回传规则API调用成功\n";
        echo "  - 返回数据条数: " . count($data['data']['list']) . "\n";
    } else {
        echo "✗ 回传规则API返回错误: " . ($data['msg'] ?? '未知错误') . "\n";
    }
} else {
    echo "✗ 回传规则API调用失败，HTTP状态码: $httpCode\n";
}

echo "\n测试完成！\n";
echo "\n建议:\n";
echo "1. 如果表结构有问题，请执行 database/update_schema.sql 脚本\n";
echo "2. 检查错误日志文件以获取详细的数据处理信息\n";
echo "3. 如果API调用失败，请检查网络连接和API配置\n";
?>
