<?php
// download_excel.php - Excel文件下载处理

require_once 'config.php';

if (!isset($_POST['excel_data'])) {
    die('缺少Excel数据');
}

// 解码数据
$batchData = json_decode(base64_decode($_POST['excel_data']), true);
if (!$batchData) {
    die('Excel数据格式错误');
}

// 设置Excel文件头
$filename = 'promotion_links_' . date('Y-m-d_H-i-s') . '.xlsx';

// 创建简单的Excel内容（使用CSV格式，但设置为Excel MIME类型）
$csvContent = "\xEF\xBB\xBF"; // UTF-8 BOM

// Excel表头（根据示例图设计）
$headers = [
    '序号',
    '推广链接ID', 
    '推广链接名称',
    '推广链接',
    '应用名称',
    'AppID',
    '广告主账户ID',
    '创建时间',
    '状态'
];

$csvContent .= implode(',', array_map(function($header) {
    return '"' . str_replace('"', '""', $header) . '"';
}, $headers)) . "\r\n";

// 连接数据库获取详细信息
$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
if ($conn->connect_error) {
    error_log("Excel生成时数据库连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

// 添加数据行
$rowNumber = 1;
if (!empty($batchData['results'])) {
    foreach ($batchData['results'] as $result) {
        $linkData = $result['link_data'];
        
        // 从link表查询详细信息
        $linkId = $linkData['id'] ?? '';
        $appName = '';
        $appId = '';
        $advertiserId = '';
        
        if ($conn && $linkId) {
            // 根据推广链接查询详细信息
            $linkUrl = $linkData['link'] ?? '';
            if ($linkUrl) {
                $stmt = $conn->prepare("SELECT app_name, appid, advertiser_account_id FROM link WHERE link = ? LIMIT 1");
                $stmt->bind_param("s", $linkUrl);
                $stmt->execute();
                $linkResult = $stmt->get_result();
                if ($linkRow = $linkResult->fetch_assoc()) {
                    $appName = $linkRow['app_name'] ?? '';
                    $appId = $linkRow['appid'] ?? '';
                    $advertiserId = $linkRow['advertiser_account_id'] ?? '';
                }
            }
        }
        
        $row = [
            $rowNumber++,
            $linkId,
            $result['name'] ?? '',
            $linkData['link'] ?? '',
            $appName,
            $appId,
            $advertiserId,
            date('Y-m-d H:i:s'),
            '成功'
        ];
        
        $csvContent .= implode(',', array_map(function($cell) {
            return '"' . str_replace('"', '""', $cell) . '"';
        }, $row)) . "\r\n";
    }
}

// 添加失败的记录
if (!empty($batchData['errors'])) {
    foreach ($batchData['errors'] as $error) {
        $row = [
            $rowNumber++,
            '',
            $error['name'] ?? '',
            '',
            '',
            '',
            '',
            date('Y-m-d H:i:s'),
            '失败: ' . ($error['error'] ?? '')
        ];
        
        $csvContent .= implode(',', array_map(function($cell) {
            return '"' . str_replace('"', '""', $cell) . '"';
        }, $row)) . "\r\n";
    }
}

if ($conn) {
    $conn->close();
}

// 设置下载头
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Cache-Control: max-age=1');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
header('Cache-Control: cache, must-revalidate');
header('Pragma: public');

// 输出Excel内容
echo $csvContent;
?>
