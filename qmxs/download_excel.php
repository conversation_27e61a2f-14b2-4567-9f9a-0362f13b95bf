<?php
// download_excel.php - Excel文件下载处理

require_once 'config.php';

if (!isset($_POST['excel_data'])) {
    die('缺少Excel数据');
}

// 解码数据
$batchData = json_decode(base64_decode($_POST['excel_data']), true);
if (!$batchData) {
    die('Excel数据格式错误');
}

// 设置Excel文件头 - 使用.xls格式以确保兼容性
$filename = 'promotion_links_' . date('Y-m-d_H-i-s') . '.xls';

// 连接数据库获取详细信息
$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
if ($conn->connect_error) {
    error_log("Excel生成时数据库连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

// 开始输出Excel内容
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Pragma: public');

// 输出Excel XML格式
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<?mso-application progid="Excel.Sheet"?>' . "\n";
echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
echo ' xmlns:o="urn:schemas-microsoft-com:office:office"' . "\n";
echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"' . "\n";
echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
echo ' xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";

// 定义样式
echo '<Styles>' . "\n";
echo '<Style ss:ID="Header">' . "\n";
echo '<Font ss:Bold="1"/>' . "\n";
echo '<Interior ss:Color="#CCE5FF" ss:Pattern="Solid"/>' . "\n";
echo '<Borders>' . "\n";
echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '</Borders>' . "\n";
echo '</Style>' . "\n";
echo '<Style ss:ID="Data">' . "\n";
echo '<Borders>' . "\n";
echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '</Borders>' . "\n";
echo '</Style>' . "\n";
echo '</Styles>' . "\n";

echo '<Worksheet ss:Name="推广链接">' . "\n";
echo '<Table>' . "\n";

// 设置列宽
echo '<Column ss:Width="120"/>' . "\n"; // 推广链接ID
echo '<Column ss:Width="100"/>' . "\n"; // 小程序名称
echo '<Column ss:Width="300"/>' . "\n"; // 小程序链接
echo '<Column ss:Width="150"/>' . "\n"; // 链接名称

// 表头
echo '<Row>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">广告主账户ID</Data></Cell>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序名称(11位)</Data></Cell>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序链接(32位)</Data></Cell>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">链接名称</Data></Cell>' . "\n";
echo '</Row>' . "\n";

// 数据行 - 只添加成功的记录
if (!empty($batchData['results'])) {
    foreach ($batchData['results'] as $result) {
        $linkData = $result['link_data'];

        // 从link表查询详细信息
        $linkId = $linkData['id'] ?? '';
        $appName = '';
        $linkUrl = $linkData['link'] ?? '';
        $linkName = $result['name'] ?? '';
        $advertiserId = '';

        if ($conn && $linkUrl) {
            $stmt = $conn->prepare("SELECT app_name, advertiser_account_id FROM link WHERE link = ? LIMIT 1");
            $stmt->bind_param("s", $linkUrl);
            $stmt->execute();
            $linkResult = $stmt->get_result();
            if ($linkRow = $linkResult->fetch_assoc()) {
                $appName = $linkRow['app_name'] ?? '';
                $advertiserId = $linkRow['advertiser_account_id'] ?? '';
            }
        }

        // 如果没有查到app_name，使用默认值
        if (empty($appName)) {
            $appName = '七猫小说';
        }

        echo '<Row>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($advertiserId) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($appName) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkUrl) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkName) . '</Data></Cell>' . "\n";
        echo '</Row>' . "\n";
    }
}

echo '</Table>' . "\n";
echo '</Worksheet>' . "\n";
echo '</Workbook>' . "\n";

if ($conn) {
    $conn->close();
}
?>
