<?php
// 引入配置文件
include 'config.php';

// 创建连接
$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");

// 插入测试数据（新增IGNORE关键字处理重复数据）
$sql = "INSERT IGNORE INTO products (product_name, appid, project) 
        VALUES ('喵饼读书', 'wxe3a874175a6e6ed3', '8'),
               ('微凉故事会', 'ttc3cbb5765cfeb7b701', '6')";
// 测试文件已包含正确实现

if ($conn->query($sql) === TRUE) {
    echo "记录插入成功（注意：重复数据已被自动跳过）";
} else {
    echo "插入错误: " . $conn->error;
}

$conn->close();
?>