<?php
// test_excel_download.php - 测试Excel下载功能

require_once 'config.php';

// 模拟批量处理的成功结果
$mockBatchData = [
    'total_records' => 3,
    'processed' => 3,
    'success' => 3,
    'failed' => 0,
    'execution_time' => 3.64,
    'average_time_per_record' => 1.21,
    'results' => [
        [
            'record_id' => '34',
            'name' => '测试链接F',
            'link_data' => [
                'id' => '569446970499879667',
                'link' => 'pages/index/index?id=515208&cnum=1&channel=rw-fx29a8819dc36d4d3820781a042cbebadc',
                'ad_monitor_click_link' => null
            ],
            'status' => 'success'
        ],
        [
            'record_id' => '35',
            'name' => '测试链接G',
            'link_data' => [
                'id' => '569446973385560333',
                'link' => 'pages/index/index?id=515208&cnum=2&channel=rw-fx4517d21c3673663bcb7d2a4d90f32676',
                'ad_monitor_click_link' => null
            ],
            'status' => 'success'
        ],
        [
            'record_id' => '36',
            'name' => '测试链接H',
            'link_data' => [
                'id' => '569446976260010883',
                'link' => 'pages/index/index?id=515208&cnum=3&channel=rw-fx073d9ef198492a75d952b3414504a20d',
                'ad_monitor_click_link' => null
            ],
            'status' => 'success'
        ]
    ],
    'errors' => []
];

// 设置Excel文件头 - 使用.xls格式以确保兼容性
$filename = 'test_promotion_links_' . date('Y-m-d_H-i-s') . '.xls';

// 连接数据库获取详细信息
$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
if ($conn->connect_error) {
    error_log("Excel生成时数据库连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

// 开始输出Excel内容
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Pragma: public');

// 输出Excel XML格式
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<?mso-application progid="Excel.Sheet"?>' . "\n";
echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
echo ' xmlns:o="urn:schemas-microsoft-com:office:office"' . "\n";
echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"' . "\n";
echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
echo ' xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";

// 定义样式
echo '<Styles>' . "\n";
echo '<Style ss:ID="Header">' . "\n";
echo '<Font ss:Bold="1"/>' . "\n";
echo '<Interior ss:Color="#CCE5FF" ss:Pattern="Solid"/>' . "\n";
echo '<Borders>' . "\n";
echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '</Borders>' . "\n";
echo '</Style>' . "\n";
echo '<Style ss:ID="Data">' . "\n";
echo '<Borders>' . "\n";
echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
echo '</Borders>' . "\n";
echo '</Style>' . "\n";
echo '</Styles>' . "\n";

echo '<Worksheet ss:Name="推广链接">' . "\n";
echo '<Table>' . "\n";

// 设置列宽
echo '<Column ss:Width="120"/>' . "\n"; // 广告主账户ID
echo '<Column ss:Width="100"/>' . "\n"; // 小程序名称
echo '<Column ss:Width="180"/>' . "\n"; // 小程序原始ID
echo '<Column ss:Width="300"/>' . "\n"; // 小程序链接
echo '<Column ss:Width="150"/>' . "\n"; // 链接名称

// 表头
echo '<Row>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">广告主账户ID</Data></Cell>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序名称(11位)</Data></Cell>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序原始ID</Data></Cell>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序链接(32位)</Data></Cell>' . "\n";
echo '<Cell ss:StyleID="Header"><Data ss:Type="String">链接名称</Data></Cell>' . "\n";
echo '</Row>' . "\n";

// 数据行 - 只添加成功的记录
if (!empty($mockBatchData['results'])) {
    foreach ($mockBatchData['results'] as $result) {
        $linkData = $result['link_data'];

        // 从link表查询详细信息
        $linkId = $linkData['id'] ?? '';
        $appName = '';
        $appId = '';
        $linkUrl = $linkData['link'] ?? '';
        $linkName = $result['name'] ?? '';
        $advertiserId = '';

        if ($conn && $linkUrl) {
            $stmt = $conn->prepare("SELECT app_name, appid, advertiser_account_id FROM link WHERE link = ? LIMIT 1");
            $stmt->bind_param("s", $linkUrl);
            $stmt->execute();
            $linkResult = $stmt->get_result();
            if ($linkRow = $linkResult->fetch_assoc()) {
                $appName = $linkRow['app_name'] ?? '';
                $appId = $linkRow['appid'] ?? '';
                $advertiserId = $linkRow['advertiser_account_id'] ?? '';
            }
        }

        // 如果没有查到数据，使用默认值
        if (empty($appName)) {
            $appName = '七猫小说';
        }
        if (empty($appId)) {
            $appId = 'wxe3a874175a6e6ed3';
        }

        echo '<Row>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($advertiserId) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($appName) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($appId) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkUrl) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkName) . '</Data></Cell>' . "\n";
        echo '</Row>' . "\n";
    }
}

echo '</Table>' . "\n";
echo '</Worksheet>' . "\n";
echo '</Workbook>' . "\n";

if ($conn) {
    $conn->close();
}
?>
