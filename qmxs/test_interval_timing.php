<?php
// test_interval_timing.php - 测试1.5秒间隔的批量创建推广链接

// 设置内部编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>1.5秒间隔测试</title></head>";
echo "<body>";
echo "<h1>批量创建推广链接 - 1.5秒间隔测试</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. 检查待处理数据</h2>";
    
    // 查询表中的数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>create_promotion_link 表中共有 <strong>$count</strong> 条记录</p>";
    
    if ($count > 0) {
        // 显示记录详情
        $stmt = $pdo->prepare("SELECT id, name, appid, book_id, chapter_num FROM create_promotion_link ORDER BY id ASC");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>待处理记录:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><th>ID</th><th>名称</th><th>AppID</th><th>书籍ID</th><th>章节序号</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . htmlspecialchars($record['appid']) . "</td>";
            echo "<td>" . htmlspecialchars($record['book_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['chapter_num']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⏱️ 时间间隔说明</h3>";
        echo "<ul>";
        echo "<li><strong>间隔时间:</strong> 每个推广链接创建请求之间间隔 <strong>1.5秒</strong></li>";
        echo "<li><strong>预计总时间:</strong> 约 " . ($count * 1.5) . " 秒（不包括API响应时间）</li>";
        echo "<li><strong>目的:</strong> 避免API频率限制，提高成功率</li>";
        echo "<li><strong>实现方式:</strong> 使用 usleep(1500000) 微秒级精确控制</li>";
        echo "</ul>";
        echo "</div>";
        
        if (isset($_POST['test_interval'])) {
            echo "<h2>2. 执行1.5秒间隔测试</h2>";
            echo "<p>开始调用批量创建API，记录时间间隔...</p>";
            
            $startTime = microtime(true);
            echo "<p><strong>开始时间:</strong> " . date('Y-m-d H:i:s', $startTime) . "</p>";
            
            // 调用API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/create_promotion_link.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 增加超时时间到5分钟
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            $endTime = microtime(true);
            $totalTime = round($endTime - $startTime, 2);
            
            echo "<p><strong>结束时间:</strong> " . date('Y-m-d H:i:s', $endTime) . "</p>";
            echo "<p><strong>总执行时间:</strong> {$totalTime}秒</p>";
            
            $expectedTime = ($count - 1) * 1.5; // 第一个请求不需要等待
            echo "<p><strong>预期间隔时间:</strong> {$expectedTime}秒</p>";
            echo "<p><strong>平均每个请求时间:</strong> " . round($totalTime / $count, 2) . "秒</p>";
            
            if ($curlError) {
                echo "<p style='color: red;'><strong>CURL错误:</strong> $curlError</p>";
            }
            
            echo "<h3>API调用结果</h3>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            
            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    if ($result['code'] === 0) {
                        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 批量处理完成！</h4>";
                        echo "<ul style='margin: 0;'>";
                        echo "<li><strong>总处理数量:</strong> " . $result['data']['total_processed'] . "</li>";
                        echo "<li><strong>成功数量:</strong> " . $result['data']['success_count'] . "</li>";
                        echo "<li><strong>失败数量:</strong> " . $result['data']['fail_count'] . "</li>";
                        echo "</ul>";
                        echo "</div>";
                        
                        // 时间效率分析
                        echo "<h3>⏱️ 时间效率分析</h3>";
                        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
                        echo "<tr><th>指标</th><th>值</th><th>说明</th></tr>";
                        echo "<tr><td>总记录数</td><td>$count</td><td>待处理的推广链接数量</td></tr>";
                        echo "<tr><td>实际执行时间</td><td>{$totalTime}秒</td><td>从开始到结束的总时间</td></tr>";
                        echo "<tr><td>预期间隔时间</td><td>{$expectedTime}秒</td><td>理论上的间隔等待时间</td></tr>";
                        echo "<tr><td>API响应时间</td><td>" . round($totalTime - $expectedTime, 2) . "秒</td><td>实际API调用和处理时间</td></tr>";
                        echo "<tr><td>平均单个请求</td><td>" . round($totalTime / $count, 2) . "秒</td><td>包含间隔和API响应的平均时间</td></tr>";
                        echo "</table>";
                        
                        // 显示成功和失败的详情
                        if (!empty($result['data']['results'])) {
                            echo "<h4>✅ 成功创建的链接:</h4>";
                            echo "<ul>";
                            foreach ($result['data']['results'] as $success) {
                                echo "<li>记录ID " . $success['record_id'] . ": " . htmlspecialchars($success['name']) . "</li>";
                            }
                            echo "</ul>";
                        }
                        
                        if (!empty($result['data']['errors'])) {
                            echo "<h4>❌ 处理失败的记录:</h4>";
                            echo "<ul>";
                            foreach ($result['data']['errors'] as $error) {
                                echo "<li style='color: red;'>记录ID " . $error['record_id'] . " (" . htmlspecialchars($error['name']) . "): " . htmlspecialchars($error['error']) . "</li>";
                            }
                            echo "</ul>";
                        }
                    } else {
                        echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #721c24; margin: 0;'>❌ API返回错误</h4>";
                        echo "<p style='margin: 10px 0 0 0;'><strong>错误代码:</strong> " . $result['code'] . "</p>";
                        echo "<p style='margin: 5px 0 0 0;'><strong>错误信息:</strong> " . htmlspecialchars($result['msg']) . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ API响应解析失败</p>";
                    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
                    echo htmlspecialchars(substr($response, 0, 1000));
                    echo "</pre>";
                }
            } else {
                echo "<p style='color: red;'>❌ API无响应</p>";
            }
        } else {
            echo "<h2>2. 开始测试</h2>";
            echo "<form method='post'>";
            echo "<p>点击下面的按钮来测试1.5秒间隔的批量创建推广链接功能：</p>";
            echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>⚠️ 注意：</strong></p>";
            echo "<ul>";
            echo "<li>测试将按照1.5秒间隔依次处理每条记录</li>";
            echo "<li>预计总耗时约 " . ($count * 1.5) . " 秒</li>";
            echo "<li>请耐心等待，不要刷新页面</li>";
            echo "</ul>";
            echo "</div>";
            echo "<button type='submit' name='test_interval' style='padding: 15px 30px; background-color: #28a745; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>🚀 开始1.5秒间隔测试</button>";
            echo "</form>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ create_promotion_link 表中没有数据，请先通过主页面添加一些推广链接参数。</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>间隔时间优化说明</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
echo "<h3>为什么使用1.5秒间隔？</h3>";
echo "<ul>";
echo "<li><strong>避免频率限制:</strong> 防止API服务器因请求过于频繁而拒绝服务</li>";
echo "<li><strong>提高成功率:</strong> 给API服务器足够的处理时间</li>";
echo "<li><strong>平衡效率:</strong> 在速度和稳定性之间找到最佳平衡点</li>";
echo "<li><strong>精确控制:</strong> 使用 usleep() 函数实现微秒级精确控制</li>";
echo "</ul>";
echo "<h3>技术实现:</h3>";
echo "<code>usleep(1500000); // 1500000微秒 = 1.5秒</code>";
echo "</div>";

echo "</body></html>";
?>
