<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量创建链接</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            border: 1px solid #ddd;
            background-color: white;
        }

        .header {
            background-color: #1e90ff;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h2 {
            margin: 0;
            font-size: 16px;
            font-weight: normal;
        }

        .create-btn {
            background-color: #00bfff;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 3px;
            cursor: pointer;
        }

        .form-container {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }

        .button-group {
            display: flex;
            margin-top: 5px;
        }

        .button-group button {
            margin-right: 5px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
        }

        .textarea-container {
            display: flex;
            gap: 20px;
        }

        .textarea-box {
            flex: 1;
        }

        .textarea-box textarea {
            width: 100%;
            height: 120px;
            resize: vertical;
        }

        .textarea-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .textarea-label span {
            color: #ff4500;
            font-size: 12px;
        }

        select.form-control {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url('data:image/svg+xml;utf8,<svg fill="gray" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>');
            background-repeat: no-repeat;
            background-position: right 8px center;
            padding-right: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>批量创建链接（草稿）</h2>
            <button class="create-btn">创建链接</button>
        </div>

        <div class="form-container">
            <div class="form-group">
                <label>创建账号</label>
                <select class="form-control" id="account-select">
                    <option value="" disabled selected>加载中...</option>
                </select>
            </div>

            <div class="form-group">
                <label>链接名称（链接名称中的应的标签会替换成对应的内容）</label>
                <div class="input-with-buttons">
                    <input type="text" class="form-control" value="<日期>-<书名>-<价值模板>-<PID>">
                    <div class="button-group">
                        <button>+日期</button>
                        <button>+书名</button>
                        <button>+价值模板</button>
                        <button>+PID</button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>资产备注信息（链接名称中的应的标签会替换成对应的内容，最长15个字）</label>
                <div class="input-with-buttons">
                    <input type="text" class="form-control" value="<日期>-<书名>">
                    <div class="button-group">
                        <button>+日期</button>
                        <button>+书名</button>
                        <button>+价值模板</button>
                        <button>+PID</button>
                    </div>
                </div>
            </div>

            <div class="textarea-container">
                <div class="textarea-box">
                    <div class="textarea-label">
                        <label>书籍ID（一行一个ID）</label>
                        <span>（已输入0个书籍ID）</span>
                    </div>
                    <textarea class="form-control" placeholder="请输入书籍ID，每行一个书籍ID"></textarea>
                </div>

                <div class="textarea-box">
                    <div class="textarea-label">
                        <label>广告主账户ID（一行一个ID）</label>
                        <span>（已输入0个广告主ID）</span>
                    </div>
                    <textarea class="form-control" placeholder="请输入广告主账户ID，每行一个广告主账户ID"></textarea>
                </div>
            </div>

            <div class="form-group">
                <label>链接类型</label>
                <select class="form-control">
                    <option>H5短链</option>
                </select>
            </div>

            <div class="form-group">
                <label>媒体渠道</label>
                <select class="form-control">
                    <option>巨量</option>
                    <option>广点通</option>
                    <option>百度</option>
                    <option>微博</option>
                    <option>B站</option>
                </select>
            </div>

            <div class="form-group">
                <label>推广平台</label>
                <select class="form-control">
                    <option>微信小程序</option>
                    <option>抖音小程序</option>
                </select>
            </div>

            <div class="form-group">
                <label>小程序站点</label>               
                    <select class="form-control" id="promotion-platform-select">
                        <option value="" disabled selected>加载中...</option>
                    </select>
                    <script>
                        // 加载小程序站点选项 - 修改为从recharge_templates表读取app_name
                        fetch('/modules/get_recharge_templates.php')
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('HTTP状态码异常: ' + response.status);
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (data.error) {
                                    promotionPlatformSelect.innerHTML = `<option value="">API错误: ${data.error}</option>`;
                                    return;
                                }

                                // 新增：强制设置分页参数获取全量数据
                                if(data.data.pagination && data.data.pagination.total_count > data.data.list.length) {
                                    // 重新请求获取全量数据（示例）
                                    const totalPageSize = Math.ceil(data.data.pagination.total_count / data.data.pagination.page_size);
                                    let allNames = [];
                                    
                                    // 递归获取所有分页数据
                                    const fetchAllPages = (page = 1) => {
                                        return fetch(`/modules/get_recharge_templates.php?page=${page}`)
                                            .then(res => res.json())
                                            .then(fullData => {
                                                allNames = [...allNames, ...fullData.data.list.map(i => i.app_name)];
                                                
                                                if(page < totalPageSize) {
                                                    return fetchAllPages(page + 1);
                                                }
                                                return allNames;
                                            });
                                    };

                                    fetchAllPages().then(allNames => {
                                        // 使用双重校验去重：先转小写校验，再保留原始名称显示
                                        const nameMap = new Map();
                                        allNames.forEach(name => {
                                            const key = name?.trim().toLowerCase();
                                            if (key && !nameMap.has(key)) {
                                                nameMap.set(key, name.trim());
                                            }
                                        });

                                        // 动态更新下拉框
                                        promotionPlatformSelect.innerHTML = [...nameMap.values()]
                                            .map(name => `<option value="${name}">${name}</option>`)
                                            .join('');
                                    });
                                } else {
                                    // 原有逻辑保持不变
                                    const nameMap = new Map(); // 用于精确去重
                                    data.data.list.forEach(item => {
                                        // 清理名称空格并转小写作为键
                                        const key = item.app_name?.trim().toLowerCase();
                                        // 保留原始名称显示
                                        if (key && !nameMap.has(key)) {
                                            nameMap.set(key, item.app_name.trim());
                                        }
                                    });

                                    // 动态更新小程序站点下拉选择框
                                    promotionPlatformSelect.innerHTML = [...nameMap.values()]
                                        .map(name => `<option value="${name}">${name}</option>`)
                                        .join('');
                                }
                            })
                            .catch(error => {
                                console.error('加载小程序站点失败:', error);
                                promotionPlatformSelect.innerHTML =
                                    `<option value="">加载失败: ${error.message}</option>`;
                            });
                    </script>
            </div>

            <div class="form-group">
                <label>回传规则</label>
                <select class="form-control" id="recharge-template-select">
                    <option value="" disabled selected>请选择回传规则名称</option>
                </select>
            </div>

            <div class="form-group">
                <label>充值模板</label>
                <select class="form-control">
                    <option value="" disabled selected>请选择充值模板名称 </option>
                </select>
            </div>
        </div>
    </div>

    <script>
        // 动态加载子账号信息
        const accountSelect = document.getElementById('account-select');
        const rechargeTemplateSelect = document.getElementById('recharge-template-select');
        const promotionPlatformSelect = document.getElementById('promotion-platform-select');

        // 原有推广平台加载逻辑保持不变
        fetch('/modules/get_recharge_templates.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP状态码异常: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('JSON解析失败: ' + e.message + '\n原始响应: ' + text.substring(0, 200));
                    }
                });
            })
            .then(data => {
                if (data.error) {
                    promotionPlatformSelect.innerHTML = `<option value="">API错误: ${data.error}</option>`;
                    return;
                }
    
                // 修改数据结构适配get_recharge_templates.php的响应格式
                const names = [...new Set(data.data.list
                    .map(item => item.app_name)  // 使用app_name字段
                    .filter(name => name && name.trim()))];
    
                // 动态更新推广平台下拉选择框
                promotionPlatformSelect.innerHTML = names
                    .map(name => `<option value="${name}">${name}</option>`)
                    .join('');
    
                // 监听推广平台选择变化，动态加载其他相关数据
                promotionPlatformSelect.addEventListener('change', () => {
                    const selectedPlatform = promotionPlatformSelect.value;
    
                    // 根据选择的推广平台加载其他相关数据
                    switch (selectedPlatform) {
                        case '喵饼读书':
                            fetch('/modules/get_return_rules.php')
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error('HTTP状态码异常: ' + response.status);
                                    }
                                    return response.text().then(text => {
                                        try {
                                            return JSON.parse(text);
                                        } catch (e) {
                                            throw new Error('JSON解析失败: ' + e.message + '\n原始响应: ' + text.substring(0, 200));
                                        }
                                    });
                                })
                                .then(data => {
                                    if (data.error) {
                                        rechargeTemplateSelect.innerHTML = `<option value="">API错误: ${data.error}</option>`;
                                        return;
                                    }
    
                                    // 修改数据结构适配get_return_rules.php的响应格式
                                    const names = [...new Set(data.data.list
                                        .map(item => item.name)  // 使用name字段
                                        .filter(name => name && name.trim()))];
    
                                    // 动态更新充值模板下拉选择框
                                    rechargeTemplateSelect.innerHTML = names
                                        .map(name => `<option value="${name}">${name}</option>`)
                                        .join('');
                                })
                                .catch(error => {
                                    console.error('加载充值模板失败:', error);
                                    rechargeTemplateSelect.innerHTML =
                                        `<option value="">加载失败: ${error.message}</option>`;
                                });
                            break;
                        case '微凉故事会':
                            // 根据需求添加其他逻辑
                            break;
                        default:
                            // 默认处理逻辑
                            break;
                    }
                });
            })
            .catch(error => {
                console.error('加载推广平台失败:', error);
                promotionPlatformSelect.innerHTML =
                    `<option value="">加载失败: ${error.message}</option>`;
            });
    
        // 原有逻辑保持不变
        fetch('/modules/get_recharge_templates.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP状态码异常: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('JSON解析失败: ' + e.message + '\n原始响应: ' + text.substring(0, 200));
                    }
                });
            })
            .then(data => {
                if (data.error) {
                    accountSelect.innerHTML = `<option value="">API错误: ${data.error}</option>`;
                    return;
                }
    
                // 修改数据结构适配get_recharge_templates.php的响应格式
                const names = [...new Set(data.data.list
                    .map(item => item.account_name)  // 使用account_name字段
                    .filter(name => name && name.trim()))];
    
                // 自动选择chenzexiong改为动态匹配
                accountSelect.innerHTML = names
                    .map(name => `<option value="${name}" ${name === 'chenzexiong' ? 'selected' : ''}>${name}</option>`)
                    .join('');
    
                // 监听子账号选择变化，动态加载充值模板
                accountSelect.addEventListener('change', () => {
                    const selectedAccountName = accountSelect.value;
    
                    fetch(`/modules/get_promotion_links.php?account_name=${encodeURIComponent(selectedAccountName)}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('HTTP状态码异常: ' + response.status);
                            }
                            return response.text().then(text => {
                                try {
                                    return JSON.parse(text);
                                } catch (e) {
                                    throw new Error('JSON解析失败: ' + e.message + '\n原始响应: ' + text.substring(0, 200));
                                }
                            });
                        })
                        .then(data => {
                            if (data.error) {
                                rechargeTemplateSelect.innerHTML = `<option value="">API错误: ${data.error}</option>`;
                                return;
                            }
    
                            // 修改数据结构适配get_promotion_links.php的响应格式
                            const names = [...new Set(data.data.list
                                .map(item => item.name)  // 使用name字段
                                .filter(name => name && name.trim()))];
    
                            // 动态更新充值模板下拉选择框
                            rechargeTemplateSelect.innerHTML = names
                                .map(name => `<option value="${name}">${name}</option>`)
                                .join('');
                        })
                        .catch(error => {
                            console.error('加载充值模板失败:', error);
                            rechargeTemplateSelect.innerHTML =
                                `<option value="">加载失败: ${error.message}</option>`;
                        });
                });
            })
            .catch(error => {
                console.error('加载子账号失败:', error);
                accountSelect.innerHTML =
                    `<option value="">加载失败: ${error.message}</option>`;
            });

        // 新增账号选择变化事件监听
        accountSelect.addEventListener('change', () => {
            const selectedAccountName = accountSelect.value;
            
            // 加载回传规则
            fetch(`/modules/get_return_rules.php?admin_account_name=${encodeURIComponent(selectedAccountName)}`)
                .then(response => {
                    if (!response.ok) throw new Error('HTTP状态码异常: ' + response.status);
                    return response.json();
                })
                .then(data => {
                    if (data.code !== 0) throw new Error(data.msg || 'API错误');
                    
                    const ruleNames = [...new Set(data.data.list
                        .filter(item => item.account_name === selectedAccountName)
                        .map(item => item.rule_name)
                        .filter(name => name))];
                    
                    rechargeTemplateSelect.innerHTML = ruleNames
                        .map(name => `<option value="${name}">${name}</option>`)
                        .join('');
                })
                .catch(error => {
                    console.error('加载回传规则失败:', error);
                    rechargeTemplateSelect.innerHTML = 
                        `<option value="">加载失败: ${error.message}</option>`;
                });

            // 加载推广链接
            fetch(`/modules/get_promotion_links.php?admin_account_name=${encodeURIComponent(selectedAccountName)}`)
                .then(response => {
                    if (!response.ok) throw new Error('HTTP状态码异常: ' + response.status);
                    return response.json();
                })
                .then(data => {
                    if (data.code !== 0) throw new Error(data.msg || 'API错误');
                    
                    const linkNames = [...new Set(data.data.list
                        .filter(item => item.account_name === selectedAccountName)
                        .map(item => item.name)
                        .filter(name => name))];
                    
                    document.querySelector('.form-group:nth-child(10) select')
                        .innerHTML = linkNames
                        .map(name => `<option value="${name}">${name}</option>`)
                        .join('');
                })
                .catch(error => {
                    console.error('加载推广链接失败:', error);
                    document.querySelector('.form-group:nth-child(10) select')
                        .innerHTML = `<option value="">加载失败: ${error.message}</option>`;
                });
        });

        // 修改：从服务器获取 app_name 并去重
        fetch('/modules/get_recharge_templates.php')
            .then(response => {
                if (!response.ok) throw new Error('HTTP状态码异常: ' + response.status);
                return response.json();
            })
            .then(data => {
                if (data.code !== 0) throw new Error(data.msg || 'API错误');

                const names = [...new Set(data.data.list
                    .map(item => item.app_name)
                    .filter(name => name && name.trim()))];

                promotionPlatformSelect.innerHTML = names
                    .map(name => `<option value="${name}">${name}</option>`)
                    .join('');
            })
            .catch(error => {
                console.error('加载小程序站点失败:', error);
                promotionPlatformSelect.innerHTML = `<option value="">加载失败: ${error.message}</option>`;
            });

    </script>
</body>
</html>