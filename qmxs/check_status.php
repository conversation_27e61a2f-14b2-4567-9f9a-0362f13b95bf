<?php
require_once 'config.php';
$pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASSWORD);

$stmt = $pdo->prepare('SELECT COUNT(*) as total FROM create_promotion_link');
$stmt->execute();
echo 'create_promotion_link: ' . $stmt->fetch(PDO::FETCH_ASSOC)['total'] . " 条记录\n";

$stmt = $pdo->prepare('SELECT COUNT(*) as total FROM link WHERE name LIKE "%直接%"');
$stmt->execute();
echo 'link表中直接测试记录: ' . $stmt->fetch(PDO::FETCH_ASSOC)['total'] . " 条记录\n";

$stmt = $pdo->prepare('SELECT COUNT(*) as total FROM link WHERE name LIKE "%提交%"');
$stmt->execute();
echo 'link表中提交测试记录: ' . $stmt->fetch(PDO::FETCH_ASSOC)['total'] . " 条记录\n";
?>
