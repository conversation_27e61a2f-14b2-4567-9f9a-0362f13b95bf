<?php
// test_batch_create_links.php - 测试批量创建推广链接功能

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>批量创建推广链接测试</title></head>";
echo "<body>";
echo "<h1>批量创建推广链接功能测试</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. 检查 create_promotion_link 表数据</h2>";
    
    // 查询表中的数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>create_promotion_link 表中共有 <strong>$count</strong> 条记录</p>";
    
    if ($count > 0) {
        // 显示前几条记录
        $stmt = $pdo->prepare("SELECT id, name, appid, book_id, chapter_num, advertiser_account_id FROM create_promotion_link ORDER BY id ASC LIMIT 5");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>前5条记录预览:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>名称</th><th>AppID</th><th>书籍ID</th><th>章节序号</th><th>广告主账户ID</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . htmlspecialchars($record['appid']) . "</td>";
            echo "<td>" . htmlspecialchars($record['book_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['chapter_num']) . "</td>";
            echo "<td>" . htmlspecialchars($record['advertiser_account_id']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>2. 检查 link 表数据</h2>";
        
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link");
        $stmt->execute();
        $linkCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<p>link 表中共有 <strong>$linkCount</strong> 条记录</p>";
        
        if ($linkCount > 0) {
            $stmt = $pdo->prepare("SELECT id, name, app_name, appid, advertiser_account_id, created_at FROM link ORDER BY id DESC LIMIT 5");
            $stmt->execute();
            $linkRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>最新5条链接记录:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>名称</th><th>应用名称</th><th>AppID</th><th>广告主账户ID</th><th>创建时间</th></tr>";
            
            foreach ($linkRecords as $record) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($record['id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['name']) . "</td>";
                echo "<td>" . htmlspecialchars($record['app_name']) . "</td>";
                echo "<td>" . htmlspecialchars($record['appid']) . "</td>";
                echo "<td>" . htmlspecialchars($record['advertiser_account_id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['created_at']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "<h2>3. 测试批量创建推广链接</h2>";
        
        if (isset($_POST['test_batch_create'])) {
            echo "<h3>执行批量创建测试...</h3>";
            
            // 调用 create_promotion_link.php
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/create_promotion_link.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "<h4>API调用结果:</h4>";
            echo "<p>HTTP状态码: <strong>$httpCode</strong></p>";
            
            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    echo "<h4>返回数据:</h4>";
                    echo "<pre style='background-color: #f5f5f5; padding: 10px; overflow-x: auto;'>";
                    echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    echo "</pre>";
                    
                    if ($result['code'] === 0) {
                        echo "<p style='color: green;'>✅ 批量创建成功！</p>";
                        echo "<ul>";
                        echo "<li>总处理数量: " . $result['data']['total_processed'] . "</li>";
                        echo "<li>成功数量: " . $result['data']['success_count'] . "</li>";
                        echo "<li>失败数量: " . $result['data']['fail_count'] . "</li>";
                        echo "</ul>";
                        
                        if (!empty($result['data']['errors'])) {
                            echo "<h4>错误详情:</h4>";
                            echo "<ul>";
                            foreach ($result['data']['errors'] as $error) {
                                echo "<li>记录ID " . $error['record_id'] . " (" . htmlspecialchars($error['name']) . "): " . htmlspecialchars($error['error']) . "</li>";
                            }
                            echo "</ul>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ 批量创建失败: " . htmlspecialchars($result['msg']) . "</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ 无法解析API响应</p>";
                    echo "<pre style='background-color: #ffe6e6; padding: 10px;'>";
                    echo htmlspecialchars($response);
                    echo "</pre>";
                }
            } else {
                echo "<p style='color: red;'>❌ API调用失败，无响应</p>";
            }
        } else {
            echo "<form method='post'>";
            echo "<p>点击下面的按钮来测试批量创建推广链接功能：</p>";
            echo "<button type='submit' name='test_batch_create' style='padding: 10px 20px; background-color: #4CAF50; color: white; border: none; cursor: pointer; font-size: 16px;'>开始批量创建测试</button>";
            echo "</form>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ create_promotion_link 表中没有数据，请先通过主页面添加一些推广链接参数。</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>4. 功能说明</h2>";
echo "<ul>";
echo "<li><strong>批量处理</strong>: create_promotion_link.php 现在会读取 create_promotion_link 表中的所有记录</li>";
echo "<li><strong>API调用</strong>: 为每条记录调用推广链接创建API</li>";
echo "<li><strong>数据存储</strong>: 成功创建的链接会存储到 link 表中</li>";
echo "<li><strong>错误处理</strong>: 单条记录失败不会影响其他记录的处理</li>";
echo "<li><strong>详细报告</strong>: 返回处理结果统计和错误详情</li>";
echo "</ul>";

echo "<h2>5. 数据流程</h2>";
echo "<ol>";
echo "<li>用户在主页面填写推广链接参数 → 存储到 create_promotion_link 表</li>";
echo "<li>执行 create_promotion_link.php → 读取所有待处理记录</li>";
echo "<li>为每条记录调用API创建推广链接</li>";
echo "<li>成功创建的链接存储到 link 表</li>";
echo "<li>返回批量处理结果</li>";
echo "</ol>";

echo "</body></html>";
?>
