-- 添加缺失的索引
-- 注意：使用前缀索引来避免键长度超限问题

-- 检查并添加 recharge_templates 表的 app_name 索引
-- 如果索引不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'recharge_templates' 
     AND index_name = 'idx_app_name') > 0,
    'SELECT "Index idx_app_name already exists on recharge_templates"',
    'ALTER TABLE recharge_templates ADD INDEX idx_app_name (app_name(50))'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 postback_rules 表的 rule_name 索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'postback_rules' 
     AND index_name = 'idx_rule_name') > 0,
    'SELECT "Index idx_rule_name already exists on postback_rules"',
    'ALTER TABLE postback_rules ADD INDEX idx_rule_name (rule_name(50))'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
