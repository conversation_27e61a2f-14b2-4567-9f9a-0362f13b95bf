-- 简化的数据库表结构更新脚本

-- 1. 删除现有的唯一键约束
ALTER TABLE `recharge_templates` DROP INDEX `unique_template`;

-- 2. 添加新的唯一键约束，包含 template_id
ALTER TABLE `recharge_templates` ADD UNIQUE KEY `unique_template` (`account_id`, `template_id`);

-- 3. 添加简单索引
ALTER TABLE `recharge_templates` ADD INDEX `idx_account_name` (`account_name`(50));
ALTER TABLE `postback_rules` ADD INDEX `idx_account_name` (`account_name`(50));
