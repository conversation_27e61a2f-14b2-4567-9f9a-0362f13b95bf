-- 简单的索引修复脚本
-- 适用于所有MySQL版本，在phpMyAdmin中逐条执行

-- 方法1：先删除再创建（如果索引存在的话）
-- 注意：如果索引不存在，删除语句会报错，但不影响后续创建

-- 删除可能存在的索引（如果不存在会报错，可以忽略）
DROP INDEX idx_account_name ON recharge_templates;
DROP INDEX idx_app_name ON recharge_templates;
DROP INDEX idx_account_name ON postback_rules;
DROP INDEX idx_rule_name ON postback_rules;

-- 创建索引
ALTER TABLE recharge_templates ADD INDEX idx_account_name (account_name(50));
ALTER TABLE recharge_templates ADD INDEX idx_app_name (app_name(50));
ALTER TABLE postback_rules ADD INDEX idx_account_name (account_name(50));
ALTER TABLE postback_rules ADD INDEX idx_rule_name (rule_name(50));
