-- 数据库表结构更新脚本
-- 修复同一子账号多条不同数据存储问题

-- 1. 删除现有的唯一键约束
ALTER TABLE `recharge_templates` DROP INDEX `unique_template`;

-- 2. 添加新的唯一键约束，包含 template_id 以允许同一子账号有多个不同模板
ALTER TABLE `recharge_templates` ADD UNIQUE KEY `unique_template` (`account_id`, `project`(30), `appid`(30), `template_id`);

-- 3. 检查 postback_rules 表的唯一键约束是否正确（应该已经包含 rule_id）
-- 如果需要，可以重新创建
-- ALTER TABLE `postback_rules` DROP INDEX `unique_postback`;
-- ALTER TABLE `postback_rules` ADD UNIQUE KEY `unique_postback` (`account_id`, `project`(30), `appid`(30), `rule_id`);

-- 4. 添加索引以提高查询性能
ALTER TABLE `recharge_templates` ADD INDEX `idx_account_name` (`account_name`);
ALTER TABLE `recharge_templates` ADD INDEX `idx_app_name` (`app_name`);
ALTER TABLE `postback_rules` ADD INDEX `idx_account_name` (`account_name`);
ALTER TABLE `postback_rules` ADD INDEX `idx_rule_name` (`rule_name`);

-- 5. 清理可能的重复数据（可选，谨慎执行）
-- DELETE t1 FROM recharge_templates t1
-- INNER JOIN recharge_templates t2
-- WHERE t1.id > t2.id
-- AND t1.account_id = t2.account_id
-- AND t1.project = t2.project
-- AND t1.appid = t2.appid
-- AND t1.template_id = t2.template_id;

-- DELETE t1 FROM postback_rules t1
-- INNER JOIN postback_rules t2
-- WHERE t1.id > t2.id
-- AND t1.account_id = t2.account_id
-- AND t1.project = t2.project
-- AND t1.appid = t2.appid
-- AND t1.rule_id = t2.rule_id;
