-- 兼容所有MySQL版本的索引创建脚本
-- 注意：如果索引已存在，会报错但不影响功能

-- 为 recharge_templates 表创建索引
-- 1. account_name 索引（使用前缀索引避免长度限制）
-- 如果索引已存在，请忽略错误信息
ALTER TABLE recharge_templates ADD INDEX idx_account_name (account_name(50));

-- 2. app_name 索引（使用前缀索引避免长度限制）
-- 如果索引已存在，请忽略错误信息
ALTER TABLE recharge_templates ADD INDEX idx_app_name (app_name(50));

-- 为 postback_rules 表创建索引
-- 3. account_name 索引（使用前缀索引避免长度限制）
-- 如果索引已存在，请忽略错误信息
ALTER TABLE postback_rules ADD INDEX idx_account_name (account_name(50));

-- 4. rule_name 索引（使用前缀索引避免长度限制）
-- 如果索引已存在，请忽略错误信息
ALTER TABLE postback_rules ADD INDEX idx_rule_name (rule_name(50));

-- 验证索引创建结果
SELECT
    TABLE_NAME as table_name,
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    CASE WHEN NON_UNIQUE = 0 THEN 'UNIQUE' ELSE 'INDEX' END as index_type,
    SUB_PART as prefix_length
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('recharge_templates', 'postback_rules')
AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
