-- -- User表（主账号）
-- CREATE TABLE IF NOT EXISTS `users` (
--   `id` INT AUTO_INCREMENT PRIMARY KEY,
--   `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '主账号用户名',
--   `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -- 子账号表（修改sub_account_id为BIGINT）
-- CREATE TABLE `sub_accounts` (
--   `id` INT AUTO_INCREMENT PRIMARY KEY,
--   `main_account_id` INT NOT NULL COMMENT '关联主账号ID',
--   `sub_account_name` VARCHAR(255) NOT NULL COMMENT '子账号名称',
--   `sub_account_id` BIGINT NOT NULL COMMENT '子账号ID',
--   `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   FOREIGN KEY (main_account_id) REFERENCES users(id) ON DELETE CASCADE,
--   UNIQUE KEY unique_sub_account (main_account_id, sub_account_id)
-- ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -- 产品表（存储小程序信息）
-- CREATE TABLE `products` (
--   `id` INT AUTO_INCREMENT PRIMARY KEY,
--   `product_name` VARCHAR(191) NOT NULL COMMENT '小程序名称',
--   `appid` VARCHAR(191) NOT NULL COMMENT '小程序APPID',
--   `project` VARCHAR(191) NOT NULL COMMENT '分销项目标识',
--   `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   -- 修改唯一键，减少前缀索引长度
--   UNIQUE KEY unique_product (product_name(50), appid(50), project(50)) -- 使用更短的前缀索引
-- ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 充值模板表ok
CREATE TABLE `recharge_templates` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `account_id` BIGINT NOT NULL COMMENT '子账号ID',
  `account_name` VARCHAR(255) NOT NULL COMMENT '子账号名称',
  `project` VARCHAR(191) NOT NULL COMMENT '分销项目标识',
  `appid` VARCHAR(191) NOT NULL COMMENT '应用ID',
  `app_name` VARCHAR(255) NOT NULL COMMENT '应用名称',
  `template_id` INT NOT NULL COMMENT '充值模板原始ID',
  `template_name` VARCHAR(255) NOT NULL COMMENT '充值模板显示名称',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY unique_template (account_id, project(50), appid(50), template_id)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 回传规则表ok
CREATE TABLE `postback_rules` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `account_id` BIGINT NOT NULL COMMENT '子账号ID',
  `account_name` VARCHAR(255) NOT NULL COMMENT '子账号名称',
  `project` VARCHAR(191) NOT NULL COMMENT '分销项目标识',
  `appid` VARCHAR(191) NOT NULL COMMENT '应用ID',
  `app_name` VARCHAR(255) NOT NULL COMMENT '应用名称',
  `rule_id` INT NOT NULL COMMENT '回传规则ID',
  `rule_name` VARCHAR(255) NOT NULL COMMENT '回传规则名称',
  `media_id` INT NOT NULL COMMENT '媒体ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY unique_postback (account_id, project(50), appid(50), rule_id)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 获取推广链接表ok
CREATE TABLE `promotion_links` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `account_id` BIGINT NOT NULL COMMENT '子账号id',
  `account_name` VARCHAR(255) NOT NULL COMMENT '子账号名称',
  `project` INT NOT NULL COMMENT '分销项目标识',
  `appid` VARCHAR(191) NOT NULL COMMENT '小程序app_id',
  `app_name` VARCHAR(255) NOT NULL COMMENT '小程序名称',
  `promotion_link_id` VARCHAR(191) NOT NULL COMMENT '推广链接id',
  `name` VARCHAR(255) NOT NULL COMMENT '推广链接名称',
  `create_time` BIGINT NOT NULL COMMENT '推广链接的创建时间，时间戳，秒', -- 调整类型为BIGINT
  `last_modified_time` BIGINT NOT NULL COMMENT '推广链接的最后修改时间，时间戳，秒', -- 调整类型为BIGINT
  `book_id` INT NOT NULL COMMENT '推广书籍id',
  `book_name` VARCHAR(255) NOT NULL COMMENT '推广书籍名称',
  `chapter_num` INT NOT NULL COMMENT '推广书籍的章节序号',
  `media_id` INT NOT NULL COMMENT '媒体标识',
  `postback_rule_name` VARCHAR(255) NOT NULL COMMENT '回传规则名称',
  `recharge_panel_name` VARCHAR(255) NOT NULL COMMENT '充值模板名称',
  `link` VARCHAR(255) NOT NULL COMMENT '推广链接',
  `launch_page` VARCHAR(255) DEFAULT NULL COMMENT '启动页面，project=6（抖音小程序）时该字段有值',
  `launch_param` VARCHAR(255) DEFAULT NULL COMMENT '启动参数，project=6（抖音小程序）时该字段有值',
  `ad_monitor_click_link` VARCHAR(255) DEFAULT NULL COMMENT '监测链接，project=8（微信小程序）且 media_id=2（广点通）时该字段有值',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY unique_promotion_link (account_id, project, appid(50), promotion_link_id(50))
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建推广链接表ok
CREATE TABLE `create_promotion_link` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `admin_account_name` VARCHAR(50) NOT NULL COMMENT '总账号用户名',
  `account_id` BIGINT NOT NULL COMMENT '子账号id',
  `project` INT NOT NULL COMMENT '分销项目标识',
  `appid` VARCHAR(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '小程序app_id',
  `book_id` INT NOT NULL COMMENT '推广书籍id',
  `chapter_num` INT NOT NULL COMMENT '推广书籍的章节序号',
  `name` VARCHAR(255) NOT NULL COMMENT '推广链接名称',
  `media_id` INT NOT NULL COMMENT '媒体标识',
  `postback_rule_id` INT NOT NULL COMMENT '回传规则id',
  `recharge_panel_id` INT NOT NULL COMMENT '充值模板id',
  `advertiser_account_id` BIGINT NOT NULL COMMENT '广告主账户ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
  UNIQUE KEY unique_promotion_link (account_id, project, appid(50), promotion_link_id(50))
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 推广链接表
CREATE TABLE `link` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `advertiser_account_id` BIGINT NOT NULL COMMENT '广告主账户ID',
  `app_name` VARCHAR(255) NOT NULL COMMENT '小程序名称',
  `appid` VARCHAR(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '小程序app_id',
  `link` VARCHAR(255) NOT NULL COMMENT '推广链接',
  `name` VARCHAR(255) NOT NULL COMMENT '推广链接名称',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
  UNIQUE KEY unique_promotion_link (account_id, project, appid(50), promotion_link_id(50))
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- -- 子账号配置关联表（多对多关系）
-- CREATE TABLE `account_config_relations` (
--   `id` INT AUTO_INCREMENT PRIMARY KEY,
--   `sub_account_id` INT NOT NULL,
--   `recharge_template_id` INT NOT NULL,
--   `postback_rule_id` INT NOT NULL,
--   `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   FOREIGN KEY (sub_account_id) REFERENCES sub_accounts(id) ON DELETE CASCADE,
--   FOREIGN KEY (recharge_template_id) REFERENCES recharge_templates(id) ON DELETE CASCADE,
--   FOREIGN KEY (postback_rule_id) REFERENCES postback_rules(id) ON DELETE CASCADE,
--   UNIQUE KEY unique_config (sub_account_id, recharge_template_id, postback_rule_id)
-- ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


