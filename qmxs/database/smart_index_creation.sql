-- 智能索引创建脚本 - 兼容所有MySQL版本
-- 使用存储过程来检查索引是否存在，如果不存在才创建

DELIMITER $$

-- 创建一个存储过程来安全地添加索引
DROP PROCEDURE IF EXISTS AddIndexIfNotExists$$
CREATE PROCEDURE AddIndexIfNotExists(
    IN table_name VARCHAR(128),
    IN index_name VARCHAR(128), 
    IN index_columns VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;
    
    -- 检查索引是否存在
    SELECT COUNT(*) INTO index_exists
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = table_name
    AND INDEX_NAME = index_name;
    
    -- 如果索引不存在，则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' ADD INDEX ', index_name, ' (', index_columns, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT CONCAT('索引 ', index_name, ' 已成功创建在表 ', table_name, ' 上') AS result;
    ELSE
        SELECT CONCAT('索引 ', index_name, ' 已存在于表 ', table_name, ' 上') AS result;
    END IF;
END$$

DELIMITER ;

-- 使用存储过程创建索引
CALL AddIndexIfNotExists('recharge_templates', 'idx_account_name', 'account_name(50)');
CALL AddIndexIfNotExists('recharge_templates', 'idx_app_name', 'app_name(50)');
CALL AddIndexIfNotExists('postback_rules', 'idx_account_name', 'account_name(50)');
CALL AddIndexIfNotExists('postback_rules', 'idx_rule_name', 'rule_name(50)');

-- 清理存储过程
DROP PROCEDURE IF EXISTS AddIndexIfNotExists;

-- 验证索引创建结果
SELECT 
    TABLE_NAME as table_name,
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    CASE WHEN NON_UNIQUE = 0 THEN 'UNIQUE' ELSE 'INDEX' END as index_type,
    SUB_PART as prefix_length
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('recharge_templates', 'postback_rules')
AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
