-- 数据库索引状态报告
-- 生成时间: 2024年

-- 查看 recharge_templates 表的所有索引
SELECT 
    'recharge_templates' as table_name,
    Key_name as index_name,
    Column_name as column_name,
    CASE WHEN Non_unique = 0 THEN 'UNIQUE' ELSE 'INDEX' END as index_type,
    Sub_part as prefix_length,
    Cardinality as cardinality
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE table_schema = DATABASE() 
AND table_name = 'recharge_templates'
ORDER BY Key_name, Seq_in_index;

-- 查看 postback_rules 表的所有索引
SELECT 
    'postback_rules' as table_name,
    Key_name as index_name,
    Column_name as column_name,
    CASE WHEN Non_unique = 0 THEN 'UNIQUE' ELSE 'INDEX' END as index_type,
    Sub_part as prefix_length,
    Cardinality as cardinality
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE table_schema = DATABASE() 
AND table_name = 'postback_rules'
ORDER BY Key_name, Seq_in_index;

-- 检查索引使用情况（需要在有查询负载时运行）
-- SELECT 
--     object_schema as database_name,
--     object_name as table_name,
--     index_name,
--     count_read,
--     count_write,
--     count_fetch,
--     count_insert,
--     count_update,
--     count_delete
-- FROM performance_schema.table_io_waits_summary_by_index_usage 
-- WHERE object_schema = DATABASE()
-- AND object_name IN ('recharge_templates', 'postback_rules')
-- ORDER BY count_read DESC;
